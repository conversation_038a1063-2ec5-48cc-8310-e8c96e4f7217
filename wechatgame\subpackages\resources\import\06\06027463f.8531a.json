[1, ["ecpdLyjvZBwrvm+cedCcQy", "2cdpG5DH1N6o+FeG4hyp+R", "73FOD/zJJD1bzyzHugRtiI", "1bpzEIMj5OULRAsfGD3mYX", "1bcgHjHEhKuLDgM9aZwwQY", "ffDpHHVcZAhqOfy25Fe4w7", "ed0hW5J5ZKBar1gflskoHO"], ["node", "_spriteFrame", "root", "panel", "messageLabel", "btn_close", "checkmark", "input", "data", "_textureSetter"], [["cc.Node", ["_name", "_opacity", "_components", "_prefab", "_contentSize", "_trs", "_parent", "_children", "_color"], 1, 9, 4, 5, 7, 1, 2, 5], ["cc.Widget", ["_alignFlags", "_originalWidth", "_originalHeight", "_top", "alignMode", "_right", "_isAbsRight", "_isAbsTop", "_left", "node"], -6, 1], ["cc.Sprite", ["_type", "_sizeMode", "node", "_materials", "_spriteFrame"], 1, 1, 3, 6], ["cc.Label", ["_string", "_fontSize", "_lineHeight", "_N$horizontalAlign", "_N$verticalAlign", "_N$overflow", "_styleFlags", "node", "_materials"], -4, 1, 3], ["cc.Node", ["_name", "_obj<PERSON><PERSON>s", "_active", "_parent", "_components", "_prefab", "_contentSize", "_trs", "_anchorPoint", "_color"], 0, 1, 12, 4, 5, 7, 5, 5], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_contentSize", "_children", "_trs"], 2, 1, 2, 4, 5, 2, 7], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], "cc.SpriteFrame", ["cc.Prefab", ["_name"], 2], ["662b2EyfNRMcIspoPI0wEDl", ["node", "input", "checkmark", "btn_close", "messageLabel", "panel"], 3, 1, 1, 1, 1, 1, 1], ["cc.BlockInputEvents", ["node"], 3, 1], ["cc.<PERSON><PERSON>", ["_N$transition", "node", "clickEvents"], 2, 1, 9], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1], ["cc.EditBox", ["max<PERSON><PERSON><PERSON>", "node", "_N$textLabel", "_N$placeholderLabel", "_N$background"], 2, 1, 1, 1, 1]], [[6, 0, 1, 2, 2], [4, 0, 3, 4, 5, 6, 7, 2], [1, 0, 1, 2, 9, 4], [1, 4, 0, 8, 1, 2, 9, 6], [11, 0, 1, 2, 2], [12, 0, 1, 2, 3], [8, 0, 2], [0, 0, 7, 2, 3, 4, 5, 2], [0, 0, 6, 7, 2, 3, 4, 5, 2], [0, 0, 1, 6, 2, 3, 4, 3], [0, 0, 6, 2, 3, 4, 5, 2], [0, 0, 6, 2, 3, 8, 4, 5, 2], [5, 0, 1, 5, 2, 3, 4, 6, 2], [5, 0, 1, 2, 3, 4, 2], [4, 0, 1, 3, 4, 5, 6, 3], [4, 0, 1, 2, 3, 4, 5, 6, 8, 7, 4], [4, 0, 1, 3, 4, 5, 9, 6, 8, 7, 3], [1, 0, 5, 3, 6, 9, 5], [1, 0, 3, 7, 9, 4], [1, 4, 0, 1, 2, 9, 5], [9, 0, 1, 2, 3, 4, 5, 1], [6, 1, 2, 1], [2, 0, 2, 3, 4, 2], [2, 0, 1, 2, 3, 4, 3], [2, 2, 3, 4, 1], [2, 1, 2, 3, 4, 2], [2, 0, 1, 2, 3, 3], [10, 0, 1], [3, 0, 1, 2, 6, 3, 4, 7, 8, 7], [3, 0, 1, 2, 3, 4, 7, 8, 6], [3, 1, 2, 5, 7, 4], [3, 0, 1, 2, 5, 7, 8, 5], [3, 0, 3, 4, 7, 8, 4], [13, 0, 1, 2, 3, 4, 2]], [[[[6, "InvitationUI"], [7, "InvitationUI", [-9, -10], [[2, 45, 750, 1334, -2], [20, -8, -7, -6, -5, -4, -3]], [21, -1, 0], [5, 750, 1334], [375, 667, 0, 0, 0, 0, 1, 1, 1, 0]], [8, "panel", 1, [-12, -13, -14, -15, -16, -17], [[22, 1, -11, [11], 12]], [0, "3bKrzFWUJB+69Z7u2dcIN1", 1, 0], [5, 624, 729], [0, 0, 0, 0, 0, 0, 1, 0.8, 0.8, 1]], [12, "input", 2, [-19, -20, -21], [-18], [0, "880FQ1rtRKsY/h/Lzqnvw1", 1, 0], [5, 460, 100], [2.846, -7.685, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "bg", 125, 1, [[23, 1, 0, -22, [0], 1], [27, -23], [2, 45, 30, 30, -24]], [0, "a2SYgDcpZCGKrTHj8Vv707", 1, 0], [5, 750, 1334]], [1, "btn_close", 2, [[[24, -25, [3], 4], -26, [17, 33, 0.033522435897435886, -2.642999999999944, false, -27]], 4, 1, 4], [0, "banNNhHd9Gvp4PIJjBGCKq", 1, 0], [5, 120, 120], [231.082, 307.1429999999999, 0, 0, 0, 0, 1, 1, 1, 0]], [10, "New Label", 2, [[28, "邀请码", 70, 70, 1, 1, 1, -28, [2]], [18, 17, 0.032716049382716106, false, -29]], [0, "2fLebT0nBML6CQkbhhNNKe", 1, 0], [5, 210, 88.2], [0, 296.54999999999995, 0, 0, 0, 0, 1, 1, 1, 1]], [14, "BACKGROUND_SPRITE", 512, 3, [[-30, [19, 0, 45, 160, 40, -31]], 1, 4], [0, "9cr/HFoq5DfK1M9XjpwwdQ", 1, 0], [5, 460, 100]], [15, "TEXT_LABEL", 512, false, 3, [[-32, [3, 0, 45, 2, 158, 40, -33]], 1, 4], [0, "fa73W1ExlEw4yg/zxrkvBk", 1, 0], [5, 158, 40], [0, 0, 1], [-78, 20, 0, 0, 0, 0, 1, 1, 1, 1]], [16, "PLACEHOLDER_LABEL", 512, 3, [[-34, [3, 0, 45, 2, 158, 40, -35]], 1, 4], [0, "bb2QqA4zVBib3GbLoIXRiB", 1, 0], [4, 4290493371], [5, 458, 100], [0, 0, 1], [-228, 50, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "checkMark", 2, [[[25, 0, -36, [8], 9], -37], 4, 1], [0, "c5zecXAHlKqoIgaWIHceC+", 1, 0], [5, 120.00000000000057, 122.00000000000028], [190.022, -242.742, 0, 0, 0, 0, 1, 1, 1, 0]], [4, 3, 5, [[5, "662b2EyfNRMcIspoPI0wEDl", "onClickClose", 1]]], [11, "title", 2, [[29, "请输入邀请码：", 60, 60, 1, 1, -38, [5]]], [0, "f5q2ndkV1DWo49m/U+Njyn", 1, 0], [4, 4278936098], [5, 420, 75.6], [-65.118, 128.06, 0, 0, 0, 0, 1, 1, 1, 1]], [26, 1, 0, 7, [6]], [30, 20, 25, 1, 8], [31, "请输入6位邀请码", 20, 25, 1, 9, [7]], [33, 6, 3, 14, 15, 13], [4, 3, 10, [[5, "662b2EyfNRMcIspoPI0wEDl", "onClickCheckmark", 1]]], [13, "messageLabel", 2, [-39], [0, "4dmHfnck1BzZ6+YeCfpjgt", 1, 0], [5, 97.87, 50.4]], [32, "Label", 1, 1, 18, [10]]], 0, [0, 2, 1, 0, 0, 1, 0, 3, 2, 0, 4, 19, 0, 5, 11, 0, 6, 17, 0, 7, 16, 0, 0, 1, 0, -1, 4, 0, -2, 2, 0, 0, 2, 0, -1, 6, 0, -2, 5, 0, -3, 12, 0, -4, 3, 0, -5, 10, 0, -6, 18, 0, -1, 16, 0, -1, 7, 0, -2, 8, 0, -3, 9, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0, 5, 0, -2, 11, 0, 0, 5, 0, 0, 6, 0, 0, 6, 0, -1, 13, 0, 0, 7, 0, -1, 14, 0, 0, 8, 0, -1, 15, 0, 0, 9, 0, 0, 10, 0, -2, 17, 0, 0, 12, 0, -1, 19, 0, 8, 1, 39], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 13], [-1, 1, -1, -1, 1, -1, -1, -1, -1, 1, -1, -1, 1, 1], [0, 1, 0, 0, 2, 0, 0, 0, 0, 3, 0, 0, 4, 5]], [[{"name": "default_editbox_bg", "rect": [0, 0, 40, 40], "offset": [0, 0], "originalSize": [40, 40], "capInsets": [12, 12, 12, 12]}], [7], 0, [0], [9], [6]]]]