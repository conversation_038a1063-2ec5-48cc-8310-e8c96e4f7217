[1, ["66tZRqKxJOvJ6Gn4CRdc5Q", "99fXsdne5I1Y5/3eXlHoTB", "ecpdLyjvZBwrvm+cedCcQy", "d8ftoL6wdB+6kFPqLDoM0P"], ["node", "_textureSetter", "root", "province", "provinceName", "_N$target", "data", "_N$normalSprite", "_N$pressedSprite", "_N$hoverSprite", "selected<PERSON><PERSON>onFrame", "normalButtonFrame", "_spriteFrame"], [["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], "cc.SpriteFrame", ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_children", "_components", "_prefab", "_contentSize", "_trs"], 2, 2, 9, 4, 5, 7], ["cc.Node", ["_name", "_parent", "_children", "_components", "_prefab", "_contentSize"], 2, 1, 2, 12, 4, 5], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_contentSize"], 2, 1, 2, 4, 5], ["cc.Layout", ["_resize", "_N$layoutType", "_N$spacingX", "node", "_layoutSize"], 0, 1, 5], ["ece01LsrVtC5a8V8DBunNMj", ["node", "provinceName", "province", "selected<PERSON><PERSON>onFrame", "normalButtonFrame"], 3, 1, 1, 1, 6, 6], ["cc.<PERSON><PERSON>", ["node", "clickEvents", "_N$target", "_N$normalSprite", "_N$pressedSprite", "_N$hoverSprite"], 3, 1, 9, 1, 6, 6, 6], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1], ["cc.Label", ["_string", "_fontSize", "_lineHeight", "_N$horizontalAlign", "_N$verticalAlign", "node", "_materials"], -2, 1, 3], ["cc.Sprite", ["_sizeMode", "node", "_materials"], 2, 1, 3]], [[0, 0, 1, 2, 2], [2, 0, 2], [3, 0, 1, 2, 3, 4, 5, 2], [4, 0, 1, 2, 3, 4, 5, 2], [5, 0, 1, 2, 3, 4, 2], [6, 0, 1, 2, 3, 4, 4], [7, 0, 1, 2, 3, 4, 1], [0, 1, 2, 1], [8, 0, 1, 2, 3, 4, 5, 1], [9, 0, 1, 2, 3], [10, 0, 1, 2, 3, 4, 5, 6, 6], [11, 0, 1, 2, 2]], [[[{"name": "province_btn_bg", "rect": [0, 0, 118, 56], "offset": [0, 0], "originalSize": [118, 56], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [1], [3]], [[[1, "provinceBtn"], [2, "provinceBtn", [-6], [[5, 1, 1, 10, -2, [5, 130, 70]], [6, -5, -4, -3, 5, 6]], [7, -1, 0], [5, 130, 70], [0, 55, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "province", 1, [-10], [[-7, [8, -9, [[9, "ece01LsrVtC5a8V8DBunNMj", "isClicked", 1]], -8, 2, 3, 4]], 1, 4], [0, "68kjQSee1Cc7LZPbD8I1Pu", 1, 0], [5, 130, 56]], [4, "name label", 2, [-11], [0, "b4fscMrxBL3qkD4Oiq2xvt", 1, 0], [5, 60, 37.8]], [10, "安徽", 30, 30, 1, 1, 3, [0]], [11, 0, 2, [1]]], 0, [0, 2, 1, 0, 0, 1, 0, 3, 5, 0, 4, 4, 0, 0, 1, 0, -1, 2, 0, -1, 5, 0, 5, 2, 0, 0, 2, 0, -1, 3, 0, -1, 4, 0, 6, 1, 11], [0, 0, 0, 0, 0, 0, 0, 5], [-1, -1, 7, 8, 9, 10, 11, 12], [2, 2, 0, 1, 1, 0, 1, 0]]]]