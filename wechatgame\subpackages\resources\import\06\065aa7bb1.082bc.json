[1, ["ecpdLyjvZBwrvm+cedCcQy", "9fC4mP5yRBWqIheInBsOM/", "57HDCyRRVJYohHV6DUH25x", "be9KsuhXZPm5SE73VlXLgp", "67dPutm29Mf4aYk5eOrkLS", "afkBy5EghIIZMvXIpmCyl4", "8cKYzzUH5ChaaLcIH0tnhU", "5dE5z4IlxOYbSoX1/+DAs0", "a9IwHYxk9ERrstG/9yRSO8", "f1xYPf2tlLhawu8VQHel9I", "d3zFrT1vpH45APXIAR5GG0", "f0v3iiZ+BEqr/y6YHhCKmH", "c1XAZhxhpHM6abMxpQVU7g", "71H9ThqqBM14gMZInSNpGg", "a1sauEEjhGPJSRS+1coEjL", "23EYdmKmtFXqtGcdlwcdE1", "98qoW4hmtHHoleG2sn2O9U", "d2cwmRM8tKwJZv+NutsH0s", "cbzwTQxAxAWIWTl4LEkXuK", "6607B2/3JHHLHsunVM5uvk", "1dwRz2NZVG0oXLg1vYcf0t", "79wSuhpgtNdae5COdWkywe", "d68oQ1S4BGPL3rSY8hT5Gs", "40xi+viyxExYfgPq57L6fs", "10ccX9bWhPNZBTcnekoyUI", "1e5T/d4EpDlYga82YNyR5H", "6cputyv7tCd6u+qLKbijXy", "67FohCyjFIdq+ZUsUBr/DM", "6f1QQDBXtF/5Tr7QOb4zj+", "0er+V3QURB27KX/v4macgS", "6bdUeJg4xIVadl5UsSMbXy", "e3gsxs4VNIpr+7Df4Ar3ya", "1cRvfNPj1JBohGp38ByoBm", "67ZGLI7FJMrIS6+Ssro62k", "c0rcMqinNAoJLP51r/r8nN", "636fFu7KhCj4cNLa8YOjnQ"], ["node", "_spriteFrame", "_defaultClip", "_textureSetter", "root", "score", "signboard", "provinceName", "rank", "data"], ["cc.SpriteFrame", ["cc.Node", ["_name", "_active", "_obj<PERSON><PERSON>s", "_opacity", "_prefab", "_parent", "_components", "_contentSize", "_trs", "_children", "_anchorPoint", "_color"], -1, 4, 1, 9, 5, 7, 2, 5, 5], ["cc.Sprite", ["_sizeMode", "node", "_materials", "_spriteFrame"], 2, 1, 3, 6], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_contentSize", "_trs", "_children"], 2, 1, 2, 4, 5, 7, 2], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.Label", ["_string", "_fontSize", "_N$horizontalAlign", "_N$verticalAlign", "_lineHeight", "node", "_materials"], -2, 1, 3], ["cc.Prefab", ["_name"], 2], ["cc.Layout", ["_resize", "node", "_layoutSize"], 2, 1, 5], ["6b952QNOWdHJZb93DSOncEr", ["node", "rank", "provinceName", "signboard", "score", "skins", "sign"], 3, 1, 1, 1, 1, 1, 3, 3], ["cc.Animation", ["playOnLoad", "node", "_clips", "_defaultClip"], 2, 1, 3, 6], ["cc.AnimationClip", ["_name", "_duration", "wrapMode", "curveData"], -1], ["cc.AnimationClip", ["_name", "_duration", "wrapMode", "curveData"], 0, 11]], [[4, 0, 1, 2, 2], [2, 1, 2, 3, 1], [9, 0, 1, 2, 3, 2], [2, 0, 1, 2, 3, 2], [1, 0, 5, 9, 4, 8, 2], [1, 0, 3, 5, 6, 4, 11, 7, 8, 3], [1, 0, 5, 6, 4, 7, 2], [1, 0, 1, 5, 6, 4, 7, 8, 3], [3, 0, 1, 2, 3, 4, 5, 2], [1, 0, 5, 6, 4, 7, 8, 2], [5, 0, 1, 4, 2, 3, 5, 6, 6], [10, 0, 1, 2, 3, 5], [6, 0, 2], [1, 0, 9, 6, 4, 7, 10, 2], [1, 0, 5, 9, 4, 2], [1, 0, 2, 5, 6, 4, 7, 3], [3, 0, 1, 6, 2, 3, 4, 5, 2], [7, 0, 1, 2, 2], [8, 0, 1, 2, 3, 4, 5, 6, 1], [4, 1, 2, 1], [2, 0, 1, 2, 2], [5, 0, 1, 2, 3, 5, 6, 5], [11, 0, 1, 2, 3, 4]], [[[{"name": "tiger", "rect": [2, 0, 120, 123], "offset": [0, 0.5], "originalSize": [124, 124], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [3], [9]], [[{"name": "kangaroo", "rect": [14, 6, 95, 112], "offset": [-0.5, 0], "originalSize": [124, 124], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [3], [10]], [[[12, "AnimalYard"], [13, "AnimalYard", [-8, -9, -10], [[17, 1, -2, [5, 552, 421.91]], [18, -7, -6, -5, -4, -3, [98, 99, 100, 101, 102, 103, 104, 105, 106, 107], [108, 109, 110]]], [19, -1, 0], [5, 552, 421.91], [0, 0.5, 0.34]], [14, "yard", 1, [-11, -12, -13, -14, -15, -16, -17, -18, -19, -20], [0, "c634ej8N1KyojzlhOjejoG", 1, 0]], [16, "Sign", 1, [-22, -23, -24], [-21], [0, "8dMMaDJjdFfIM0M92l+n0e", 1, 0], [5, 208, 203], [0, 176.563, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "animal container copy", 2, [-25, -26, -27, -28], [0, "3dPeLEspNKn7PtDeCEqy0V", 1, 0], [169.346, -64.456, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "animal container copy", 2, [-29, -30, -31, -32], [0, "79k0rVEwZLYIfNEuqxIkx+", 1, 0], [33.794, -73.969, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "animal container copy", 2, [-33, -34, -35, -36], [0, "83aFASSrtIaLneRE1ybcOq", 1, 0], [-66.087, -76.347, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "animal container copy", 2, [-37, -38, -39, -40], [0, "c0r4j2atpF07S39wOn6IfS", 1, 0], [-181.425, -68.023, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "animal container", 2, [-41, -42], [0, "69q3HKgxhILbbFu82kbxmb", 1, 0], [-10.201, 60.395, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "animal", 8, [[1, -43, [8], 9], [2, true, -44, [11], 10]], [0, "b9UF2Blq5NfIE+Rrpuce6W", 1, 0], [5, 96, 112]], [4, "animal container copy", 2, [-45, -46], [0, "bcaYOfSOhLK7KWp/gMRvbp", 1, 0], [-117.216, 69.907, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "animal", 10, [[1, -47, [14], 15], [2, true, -48, [17], 16]], [0, "4fejl7aLJHRY2ioLBn46W4", 1, 0], [5, 96, 112]], [4, "animal container copy", 2, [-49, -50], [0, "eah4+Ej5BClLiodT2hZQDr", 1, 0], [67.087, 43.748, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "animal", 12, [[1, -51, [20], 21], [2, true, -52, [23], 22]], [0, "ceK83pfytIb67DieXsPiQi", 1, 0], [5, 96, 112], [32.104, 9.512, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "animal container copy", 2, [-53, -54], [0, "benAMWgeJBEahV61GUoQMv", 1, 0], [125.35, -14.517, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "animal", 14, [[1, -55, [26], 27], [2, true, -56, [29], 28]], [0, "22VStMoYdObKGidcxuhyEA", 1, 0], [5, 96, 112]], [4, "animal container copy", 2, [-57, -58], [0, "ccN8oZ/5ZFX7V8mTar7sgj", 1, 0], [-132.674, -3.815, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "animal", 16, [[1, -59, [32], 33], [2, true, -60, [35], 34]], [0, "49AP2BWwhDTIQiq3ULlL6j", 1, 0], [5, 96, 112]], [4, "animal container copy", 2, [-61, -62], [0, "c6ZX9RtyBCopByG1vyQuia", 1, 0], [-11.391, -9.76, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "animal", 18, [[1, -63, [38], 39], [2, true, -64, [41], 40]], [0, "fddiDfO2dJgZw8IBjSrE6N", 1, 0], [5, 96, 112]], [6, "animal", 4, [[1, -65, [44], 45], [2, true, -66, [47], 46]], [0, "7dba4aXxpCf5+q0y+0RSBg", 1, 0], [5, 96, 112]], [7, "pointYou", false, 4, [[1, -67, [48], 49], [2, true, -68, [51], 50]], [0, "d4Fd9jQUxHCaIJ5YCh6jxD", 1, 0], [5, 90, 81], [82.946, 65.706, 0, 0, 0, 0, 1, 0.5, 0.5, 1]], [7, "you", false, 4, [[1, -69, [52], 53], [2, true, -70, [55], 54]], [0, "b0XKx5+vtBfajZt5i9Wrev", 1, 0], [5, 58, 64], [64.24, -87.301, 0, 0, 0, 0, 1, 0.8, 0.8, 1]], [9, "animal", 5, [[1, -71, [58], 59], [2, true, -72, [61], 60]], [0, "f6LOZgyD9Hw7K0uszqM2Wj", 1, 0], [5, 96, 112], [8, 4, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "pointYou", false, 5, [[1, -73, [62], 63], [2, true, -74, [65], 64]], [0, "90bug4iHhKObKw/NUZ9t+w", 1, 0], [5, 90, 81], [82.946, 65.706, 0, 0, 0, 0, 1, 0.5, 0.5, 1]], [7, "you", false, 5, [[1, -75, [66], 67], [2, true, -76, [69], 68]], [0, "46nubfuyxORqSwrV354Uo1", 1, 0], [5, 58, 64], [64.24, -87.301, 0, 0, 0, 0, 1, 0.8, 0.8, 1]], [6, "animal", 6, [[1, -77, [72], 73], [2, true, -78, [75], 74]], [0, "8fMg6cxBBAAauTGD5vogT7", 1, 0], [5, 96, 112]], [7, "pointYou", false, 6, [[1, -79, [76], 77], [2, true, -80, [79], 78]], [0, "0beC8obz5InZBpyd9QOh65", 1, 0], [5, 90, 81], [82.946, 65.706, 0, 0, 0, 0, 1, 0.5, 0.5, 1]], [7, "you", false, 6, [[1, -81, [80], 81], [2, true, -82, [83], 82]], [0, "c47qdD7DFBXqCDWEBsF8le", 1, 0], [5, 58, 64], [64.24, -87.301, 0, 0, 0, 0, 1, 0.8, 0.8, 1]], [6, "animal", 7, [[1, -83, [86], 87], [2, true, -84, [89], 88]], [0, "fbUIiqP5tAwI16GY5M68uL", 1, 0], [5, 96, 112]], [7, "pointYou", false, 7, [[1, -85, [90], 91], [2, true, -86, [93], 92]], [0, "328sYmtXlJqrIFE+bNOdr1", 1, 0], [5, 90, 81], [82.946, 65.706, 0, 0, 0, 0, 1, 0.5, 0.5, 1]], [7, "you", false, 7, [[1, -87, [94], 95], [2, true, -88, [97], 96]], [0, "ebw8OBvkBC4rJTuaLsX1Ca", 1, 0], [5, 58, 64], [64.24, -87.301, 0, 0, 0, 0, 1, 0.8, 0.8, 1]], [8, "team Label", 3, [-89], [0, "9fNjF9entHIZFRd+WK56ca", 1, 0], [5, 90, 50.4], [0, 30, 0, 0, 0, 0, 1, 1, 1, 1]], [21, "安徽队", 30, 1, 1, 32, [0]], [8, "rank Label", 3, [-90], [0, "786y/XzlJBtJLjeY9CulQ+", 1, 0], [5, 40.9, 25.2], [0, 69, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "第1名", 16, 20, 1, 1, 34, [1]], [8, "score Label", 3, [-91], [0, "54u/fMTUBG65lImol200rw", 1, 0], [5, 58.04, 25.2], [42, -25, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "2564次", 18, 20, 1, 1, 36, [2]], [20, 0, 3, [3]], [15, "fence", 512, 1, [[3, 0, -92, [4], 5]], [0, "307skdrLhJGIOaTZ4UKFcQ", 1, 0], [5, 552, 262]], [5, "FootShadow", 50, 8, [[3, 0, -93, [6], 7]], [0, "bdkMH3ZnJDvLcCN+ujzOV/", 1, 0], [4, 4278190080], [5, 80, 31], [-2, -52, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "FootShadow", 50, 10, [[3, 0, -94, [12], 13]], [0, "25LogGzXJEea/OKW7VgOx2", 1, 0], [4, 4278190080], [5, 80, 31], [-2, -52, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "FootShadow", 50, 12, [[3, 0, -95, [18], 19]], [0, "40aZoS13hOdKggqHlqJug8", 1, 0], [4, 4278190080], [5, 80, 31], [-2, -52, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "FootShadow", 50, 14, [[3, 0, -96, [24], 25]], [0, "d3GbgS6QhOHrgOrcQoK9mb", 1, 0], [4, 4278190080], [5, 80, 31], [-2, -52, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "FootShadow", 50, 16, [[3, 0, -97, [30], 31]], [0, "77IwVnDz9Fo5j/V8Fbjz7W", 1, 0], [4, 4278190080], [5, 80, 31], [-2, -52, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "FootShadow", 50, 18, [[3, 0, -98, [36], 37]], [0, "a6MuxhUvBDP5bNGbEcVQbv", 1, 0], [4, 4278190080], [5, 80, 31], [-2, -52, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "FootShadow", 50, 4, [[3, 0, -99, [42], 43]], [0, "45UoXcGxJPH7CLq/4/7w8y", 1, 0], [4, 4278190080], [5, 80, 31], [-2, -52, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "FootShadow", 50, 5, [[3, 0, -100, [56], 57]], [0, "ebYRDo7ApKYLhYAKNJ9vn4", 1, 0], [4, 4278190080], [5, 80, 31], [-2, -52, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "FootShadow", 50, 6, [[3, 0, -101, [70], 71]], [0, "edZA/P72hFaobavtqyZpIx", 1, 0], [4, 4278190080], [5, 80, 31], [-2, -52, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "FootShadow", 50, 7, [[3, 0, -102, [84], 85]], [0, "25M2Dnex9OvaOtl/FkIJWV", 1, 0], [4, 4278190080], [5, 80, 31], [-2, -52, 0, 0, 0, 0, 1, 1, 1, 1]]], 0, [0, 4, 1, 0, 0, 1, 0, 5, 37, 0, 6, 38, 0, 7, 33, 0, 8, 35, 0, 0, 1, 0, -1, 3, 0, -2, 39, 0, -3, 2, 0, -1, 8, 0, -2, 10, 0, -3, 12, 0, -4, 14, 0, -5, 16, 0, -6, 18, 0, -7, 4, 0, -8, 5, 0, -9, 6, 0, -10, 7, 0, -1, 38, 0, -1, 32, 0, -2, 34, 0, -3, 36, 0, -1, 46, 0, -2, 20, 0, -3, 21, 0, -4, 22, 0, -1, 47, 0, -2, 23, 0, -3, 24, 0, -4, 25, 0, -1, 48, 0, -2, 26, 0, -3, 27, 0, -4, 28, 0, -1, 49, 0, -2, 29, 0, -3, 30, 0, -4, 31, 0, -1, 40, 0, -2, 9, 0, 0, 9, 0, 0, 9, 0, -1, 41, 0, -2, 11, 0, 0, 11, 0, 0, 11, 0, -1, 42, 0, -2, 13, 0, 0, 13, 0, 0, 13, 0, -1, 43, 0, -2, 15, 0, 0, 15, 0, 0, 15, 0, -1, 44, 0, -2, 17, 0, 0, 17, 0, 0, 17, 0, -1, 45, 0, -2, 19, 0, 0, 19, 0, 0, 19, 0, 0, 20, 0, 0, 20, 0, 0, 21, 0, 0, 21, 0, 0, 22, 0, 0, 22, 0, 0, 23, 0, 0, 23, 0, 0, 24, 0, 0, 24, 0, 0, 25, 0, 0, 25, 0, 0, 26, 0, 0, 26, 0, 0, 27, 0, 0, 27, 0, 0, 28, 0, 0, 28, 0, 0, 29, 0, 0, 29, 0, 0, 30, 0, 0, 30, 0, 0, 31, 0, 0, 31, 0, -1, 33, 0, -1, 35, 0, -1, 37, 0, 0, 39, 0, 0, 40, 0, 0, 41, 0, 0, 42, 0, 0, 43, 0, 0, 44, 0, 0, 45, 0, 0, 46, 0, 0, 47, 0, 0, 48, 0, 0, 49, 0, 9, 1, 102], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 38], [-1, -1, -1, -1, -1, 1, -1, 1, -1, 1, 2, -1, -1, 1, -1, 1, 2, -1, -1, 1, -1, 1, 2, -1, -1, 1, -1, 1, 2, -1, -1, 1, -1, 1, 2, -1, -1, 1, -1, 1, 2, -1, -1, 1, -1, 1, 2, -1, -1, 1, 2, -1, -1, 1, 2, -1, -1, 1, -1, 1, 2, -1, -1, 1, 2, -1, -1, 1, 2, -1, -1, 1, -1, 1, 2, -1, -1, 1, 2, -1, -1, 1, 2, -1, -1, 1, -1, 1, 2, -1, -1, 1, 2, -1, -1, 1, 2, -1, -1, -2, -3, -4, -5, -6, -7, -8, -9, -10, -1, -2, -3, 1], [0, 0, 0, 0, 0, 11, 0, 3, 0, 2, 1, 1, 0, 3, 0, 2, 1, 1, 0, 3, 0, 2, 1, 1, 0, 3, 0, 2, 1, 1, 0, 3, 0, 2, 1, 1, 0, 3, 0, 2, 1, 1, 0, 3, 0, 2, 1, 1, 0, 6, 4, 4, 0, 7, 5, 5, 0, 3, 0, 2, 1, 1, 0, 6, 4, 4, 0, 7, 5, 5, 0, 3, 0, 2, 1, 1, 0, 6, 4, 4, 0, 7, 5, 5, 0, 3, 0, 2, 1, 1, 0, 6, 4, 4, 0, 7, 5, 5, 12, 2, 13, 14, 15, 16, 17, 18, 19, 20, 8, 21, 22, 8]], [[{"name": "you", "rect": [0, 1, 58, 64], "offset": [0, 0], "originalSize": [58, 66], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [3], [23]], [[{"name": "raccoon", "rect": [9, 5, 105, 113], "offset": [-0.5, 0.5], "originalSize": [124, 124], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [3], [24]], [[[11, "youAnima", 1.3333333333333333, 2, {"props": {"position": [{"frame": 0, "value": [82.946, 65.706, 0]}, {"frame": 0.3333333333333333, "value": [117.537, 93.708, 0]}, {"frame": 0.6666666666666666, "value": [82.946, 65.706, 0]}, {"frame": 1, "value": [117.537, 92.061, 0]}, {"frame": 1.3333333333333333, "value": [79.652, 64.059, 0]}]}}]], 0, 0, [], [], []], [[{"name": "cat", "rect": [11, 6, 102, 112], "offset": [0, 0], "originalSize": [124, 124], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [3], [25]], [[{"name": "redSign", "rect": [0, 3, 220, 221], "offset": [0, -1.5], "originalSize": [220, 224], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [3], [26]], [[{"name": "pointYou", "rect": [3, 2, 90, 81], "offset": [0.5, 0], "originalSize": [95, 85], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [3], [27]], [[{"name": "panda", "rect": [9, 6, 106, 112], "offset": [0, 0], "originalSize": [124, 124], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [3], [28]], [[[11, "IdleAnima", 0.9833333333333333, 2, {"props": {"scaleY": [{"frame": 0.016666666666666666, "value": 1}, {"frame": 0.15, "value": 1}, {"frame": 0.25, "value": 1.0904761904761906}, {"frame": 0.5, "value": 1}, {"frame": 0.8166666666666667, "value": 1.08}, {"frame": 0.9833333333333333, "value": 1}]}}]], 0, 0, [], [], []], [[{"name": "horse", "rect": [5, 12, 113, 100], "offset": [-0.5, 0], "originalSize": [124, 124], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [3], [29]], [[{"name": "blueSign", "rect": [0, 3, 220, 221], "offset": [0, -1.5], "originalSize": [220, 224], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [3], [30]], [[[22, "pointYou", 0.65, 2, [{}, "props", 11, [{}, "scale", 12, [[[{"frame": 0}, "value", 8, [1, 0.8, 0.8, 1]], [{"frame": 0.31666666666666665}, "value", 8, [1, 1, 1, 1]], [{"frame": 0.65}, "value", 8, [1, 0.8, 0.8, 1]]], 11, 11, 11]]]]], 0, 0, [], [], []], [[{"name": "FootShadow", "rect": [0, 0, 110, 39], "offset": [0, 0.5], "originalSize": [110, 40], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [3], [31]], [[{"name": "pig", "rect": [8, 5, 106, 114], "offset": [-1, 0], "originalSize": [124, 124], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [3], [32]], [[{"name": "parrot", "rect": [9, 7, 107, 111], "offset": [0.5, -0.5], "originalSize": [124, 124], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [3], [33]], [[{"name": "yellowSign", "rect": [0, 3, 220, 217], "offset": [0, 0.5], "originalSize": [220, 224], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [3], [34]], [[{"name": "fence", "rect": [0, 0, 590, 270], "offset": [0, 0], "originalSize": [590, 270], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [3], [35]]]]