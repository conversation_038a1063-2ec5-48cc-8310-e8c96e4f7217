{"description": "项目配置文件。", "miniprogramRoot": "./", "setting": {"urlCheck": true, "es6": true, "postcss": true, "minified": false, "newFeature": false, "compileWorklet": false, "uglifyFileName": false, "uploadWithSourceMap": true, "enhance": false, "packNpmManually": false, "packNpmRelationList": [], "minifyWXSS": true, "minifyWXML": true, "localPlugins": false, "disableUseStrict": false, "useCompilerPlugins": false, "condition": false, "swc": false, "disableSWC": true, "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}}, "compileType": "game", "libVersion": "game", "appid": "wx3f90fbee449adad6", "projectname": "cat", "condition": {"search": {"current": -1, "list": []}, "conversation": {"current": -1, "list": []}, "game": {"currentL": -1, "list": [], "current": -1}, "miniprogram": {"current": -1, "list": []}}, "simulatorPluginLibVersion": {}, "packOptions": {"ignore": [], "include": []}, "isGameTourist": false, "editorSetting": {}}