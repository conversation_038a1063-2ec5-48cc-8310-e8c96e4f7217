[1, ["ecpdLyjvZBwrvm+cedCcQy", "9ekhOp79pBBbVmHFh9AP0u", "99fXsdne5I1Y5/3eXlHoTB", "9eDdDQHrxOHp8pT15N9aVG"], ["node", "_spriteFrame", "root", "provinceScore", "provinceName", "provincePosition", "data", "_textureSetter"], [["cc.Node", ["_name", "_children", "_components", "_prefab", "_contentSize", "_trs", "_parent"], 2, 2, 9, 4, 5, 7, 1], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_contentSize", "_trs"], 2, 1, 2, 4, 5, 7], ["cc.Layout", ["_N$layoutType", "_N$spacingX", "_resize", "_N$paddingLeft", "node", "_layoutSize"], -1, 1, 5], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.Sprite", ["_sizeMode", "node", "_materials", "_spriteFrame"], 2, 1, 3, 6], "cc.SpriteFrame", ["cc.Prefab", ["_name"], 2], ["b33ecdCtO9Pua+xGvMSklb4", ["node", "provincePosition", "provinceName", "provinceScore"], 3, 1, 1, 1, 1], ["cc.Label", ["_string", "_N$horizontalAlign", "_N$verticalAlign", "node", "_materials"], 0, 1, 3]], [[3, 0, 1, 2, 2], [8, 0, 1, 2, 3, 4, 4], [0, 0, 6, 1, 2, 3, 4, 5, 2], [1, 0, 1, 2, 3, 4, 5, 2], [6, 0, 2], [0, 0, 1, 2, 3, 4, 5, 2], [1, 0, 1, 2, 3, 4, 2], [2, 2, 0, 1, 4, 5, 4], [2, 0, 3, 1, 4, 5, 4], [7, 0, 1, 2, 3, 1], [3, 1, 2, 1], [4, 0, 1, 2, 3, 2], [4, 1, 2, 3, 1]], [[[[4, "myRankRow"], [5, "myRankRow", [-7, -8], [[7, 1, 1, 10, -2, [5, 523, 100]], [9, -6, -5, -4, -3]], [10, -1, 0], [5, 523, 100], [0, -55, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "provinceName", 1, [-11, -12], [[11, 0, -9, [5], 6], [8, 1, 50, 30, -10, [5, 437, 75]]], [0, "e7wmVZVr5NLYsaey48NHS1", 1, 0], [5, 437, 75], [43, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "provincePosition", 1, [-14], [[12, -13, [1], 2]], [0, "30/kJNQ6VMjb/XtFisOLJs", 1, 0], [5, 76, 76], [-223.5, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "position Label", 3, [-15], [0, "31d9FOaqtNabBExO+Y2H4H", 1, 0], [5, 44.49, 50.4]], [1, "34", 1, 1, 4, [0]], [3, "Name Label", 2, [-16], [0, "b9WfpveV1Jra40Nk8yi/Zx", 1, 0], [5, 80, 50.4], [-128.5, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "河南", 1, 1, 6, [3]], [3, "score", 2, [-17], [0, "fb0hIwH39Aa6iNybJRkfX0", 1, 0], [5, 88.98, 50.4], [-14.009999999999998, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "2554", 1, 1, 8, [4]]], 0, [0, 2, 1, 0, 0, 1, 0, 3, 9, 0, 4, 7, 0, 5, 5, 0, 0, 1, 0, -1, 3, 0, -2, 2, 0, 0, 2, 0, 0, 2, 0, -1, 6, 0, -2, 8, 0, 0, 3, 0, -1, 4, 0, -1, 5, 0, -1, 7, 0, -1, 9, 0, 6, 1, 17], [0, 0, 0, 0, 0, 0, 0], [-1, -1, 1, -1, -1, -1, 1], [0, 0, 1, 0, 0, 0, 2]], [[{"name": "rr_my_rank", "rect": [1, 6, 76, 76], "offset": [-0.5, -3], "originalSize": [79, 82], "capInsets": [0, 0, 0, 0]}], [5], 0, [0], [7], [3]]]]