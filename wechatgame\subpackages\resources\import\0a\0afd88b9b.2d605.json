[1, ["ecpdLyjvZBwrvm+cedCcQy", "d6J5YrolVH37zG9jTA4a8u", "7cMGfLvS1BooDg8ScPoPIj", "4aUVmNCWROZalPqNtPLyh2", "daNyLDHqVFFZCLg3NFnVFE", "1dPcGbDepDCKJyoV7DL3LA", "be2F+YLRBDYbcZ62v1noZk", "2cdpG5DH1N6o+FeG4hyp+R", "81cqPqbOJETILMZVzOO/vh", "67ejKQczhLtIkZwrMUo60V", "73FOD/zJJD1bzyzHugRtiI", "83iPtOReVOo65gD6jrDeHM", "1bcgHjHEhKuLDgM9aZwwQY", "b3BiV6CoxG0pLPkqI+wUdB"], ["node", "_spriteFrame", "_textureSetter", "root", "btn_invitation", "soundSprite", "musicSprite", "data", "openFrame", "closeFrame"], ["cc.SpriteFrame", ["cc.Sprite", ["_type", "_sizeMode", "node", "_materials", "_spriteFrame"], 1, 1, 3, 6], ["cc.Node", ["_name", "_opacity", "_components", "_prefab", "_contentSize", "_trs", "_parent", "_children"], 1, 9, 4, 5, 7, 1, 2], ["cc.Widget", ["_alignFlags", "_originalWidth", "_originalHeight", "_right", "_isAbsRight", "_top", "_isAbsTop", "node"], -4, 1], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.<PERSON><PERSON>", ["_N$transition", "node", "clickEvents"], 2, 1, 9], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_contentSize", "_trs"], 2, 1, 12, 4, 5, 7], ["d2706bmYxdMlJhqZw61eAjA", ["node", "musicSprite", "soundSprite", "btn_invitation", "openFrame", "closeFrame"], 3, 1, 1, 1, 1, 6, 6], ["cc.BlockInputEvents", ["node"], 3, 1], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1], ["cc.Label", ["_string", "_fontSize", "_lineHeight", "_styleFlags", "_N$horizontalAlign", "_N$verticalAlign", "node", "_materials"], -3, 1, 3]], [[4, 0, 1, 2, 2], [10, 0, 1, 2, 3], [2, 0, 6, 2, 3, 4, 5, 2], [5, 0, 1, 2, 2], [7, 0, 1, 2, 3, 4, 5, 2], [1, 2, 3, 4, 1], [3, 0, 1, 2, 7, 4], [1, 2, 3, 1], [6, 0, 2], [2, 0, 7, 2, 3, 4, 5, 2], [2, 0, 6, 7, 2, 3, 4, 5, 2], [2, 0, 1, 6, 2, 3, 4, 3], [3, 0, 3, 4, 7, 4], [3, 0, 5, 6, 7, 4], [8, 0, 1, 2, 3, 4, 5, 1], [4, 1, 2, 1], [1, 0, 2, 3, 4, 2], [1, 0, 1, 2, 3, 4, 3], [1, 1, 2, 3, 4, 2], [9, 0, 1], [5, 1, 2, 1], [11, 0, 1, 2, 3, 4, 5, 6, 7, 7]], [[[{"name": "text_sound", "rect": [2, 2, 93, 36], "offset": [0.5, 0], "originalSize": [96, 40], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [3]], [[{"name": "icon_close", "rect": [6, 6, 118, 44], "offset": [3, -0.5], "originalSize": [124, 55], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [4]], [[{"name": "text_music", "rect": [2, 2, 93, 36], "offset": [0.5, 0], "originalSize": [96, 40], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [5]], [[{"name": "btn_invitation", "rect": [83, 174, 333, 151], "offset": [0, 0], "originalSize": [499, 499], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [6]], [[[8, "SettingUI"], [9, "view", [-7, -8], [[6, 45, 750, 1334, -2], [14, -6, -5, -4, -3, 15, 16]], [15, -1, 0], [5, 750, 1334], [375, 667, 0, 0, 0, 0, 1, 1, 1, 0]], [10, "New Sprite", 1, [-10, -11, -12, -13, -14, -15, -16], [[16, 1, -9, [13], 14]], [0, "3bKrzFWUJB+69Z7u2dcIN1", 1, 0], [5, 624, 729], [0, 0, 0, 0, 0, 0, 1, 0.8, 0.8, 1]], [11, "bg", 125, 1, [[17, 1, 0, -17, [0], 1], [19, -18], [6, 45, 30, 30, -19]], [0, "a2SYgDcpZCGKrTHj8Vv707", 1, 0], [5, 750, 1334]], [2, "New Sprite", 2, [[5, -20, [9], 10], [3, 3, -21, [[1, "d2706bmYxdMlJhqZw61eAjA", "onClickClose", 1]]], [12, 33, -0.03, false, -22]], [0, "banNNhHd9Gvp4PIJjBGCKq", 1, 0], [5, 120, 120], [270.72, 304.5, 0, 0, 0, 0, 1, 1, 1, 0]], [2, "New Label", 2, [[21, "设置", 50, 50, 1, 1, 1, -23, [2]], [13, 17, 0.05, false, -24]], [0, "2fLebT0nBML6CQkbhhNNKe", 1, 0], [5, 100, 63], [0, 296.55, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "btn_music", 2, [[-25, [3, 3, -26, [[1, "d2706bmYxdMlJhqZw61eAjA", "onClickMusic", 1]]]], 1, 4], [0, "3eBhInMaJFIJvQv1QkM8iD", 1, 0], [5, 118, 44], [74, 39.9, 0, 0, 0, 0, 1, 1.5, 1.5, 0]], [2, "text_music", 2, [[5, -27, [4], 5], [20, -28, [[1, "d2706bmYxdMlJhqZw61eAjA", "oclickNoAds", 1]]]], [0, "adhlZ+dv9P0IbS0BxGKmAB", 1, 0], [5, 93, 36], [-130.265, 37.807, 0, 0, 0, 0, 1, 1.5, 1.5, 1]], [4, "btn_sound", 2, [[-29, [3, 3, -30, [[1, "d2706bmYxdMlJhqZw61eAjA", "onClickSound", 1]]]], 1, 4], [0, "8421pwGilG3Zun1CBhACd6", 1, 0], [5, 118, 44], [74, -89, 0, 0, 0, 0, 1, 1.5, 1.5, 0]], [4, "btn_invitation", 2, [[[18, 0, -31, [11], 12], -32], 4, 1], [0, "45YhXNp6ZBEpG7JjrpYRkR", 1, 0], [5, 122, 67], [-16.505, -225.50125, 0, 0, 0, 0, 1, 1.25, 1.25, 0]], [7, 6, [3]], [7, 8, [6]], [2, "text_sound", 2, [[5, -33, [7], 8]], [0, "23bxusmuhJ4aceOz+bLEj5", 1, 0], [5, 93, 36], [-130, -89, 0, 0, 0, 0, 1, 1.5, 1.5, 1]], [3, 3, 9, [[1, "d2706bmYxdMlJhqZw61eAjA", "onClickInvitation", 1]]]], 0, [0, 3, 1, 0, 0, 1, 0, 4, 13, 0, 5, 11, 0, 6, 10, 0, 0, 1, 0, -1, 3, 0, -2, 2, 0, 0, 2, 0, -1, 5, 0, -2, 6, 0, -3, 7, 0, -4, 8, 0, -5, 12, 0, -6, 4, 0, -7, 9, 0, 0, 3, 0, 0, 3, 0, 0, 3, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0, 5, 0, 0, 5, 0, -1, 10, 0, 0, 6, 0, 0, 7, 0, 0, 7, 0, -1, 11, 0, 0, 8, 0, 0, 9, 0, -2, 13, 0, 0, 12, 0, 7, 1, 33], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 10, 11], [-1, 1, -1, -1, -1, 1, -1, -1, 1, -1, 1, -1, 1, -1, 1, 8, 9, 1, 1], [0, 7, 0, 0, 0, 8, 0, 0, 9, 0, 10, 0, 11, 0, 12, 1, 2, 2, 1]], [[{"name": "icon_open", "rect": [0, 6, 118, 44], "offset": [-2.5, -0.5], "originalSize": [123, 55], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [13]]]]