[1, ["ecpdLyjvZBwrvm+cedCcQy", "a2MjXRFdtLlYQ5ouAFv/+R", "33dZpZfD1HrZX0qp5r+fln", "6bmSHyjk1OxL1NAfIeBYkJ", "7fWYyM2SBLL6KnLF9p/ozX", "5eafFcNntFgbQVuN5iYEkF", "9c7jak+59J2Ki/KdMAOjEs", "cfNZ1wJ2JKe4SpQE3S1D0j", "baUG+3y6FJu4g6Zg+naieT"], ["node", "_spriteFrame", "_textureSetter", "root", "cat", "data"], [["cc.Sprite", ["_type", "_sizeMode", "node", "_materials", "_spriteFrame"], 1, 1, 3, 6], "cc.SpriteFrame", ["cc.Node", ["_name", "_opacity", "_components", "_prefab", "_contentSize", "_trs", "_parent", "_children", "_color"], 1, 9, 4, 5, 7, 1, 2, 5], ["cc.Widget", ["_alignFlags", "_originalWidth", "_originalHeight", "node"], 0, 1], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_contentSize", "_trs"], 2, 1, 2, 4, 5, 7], ["9a23242ZdZNUZiEOZr1iRnK", ["node", "cat"], 3, 1, 1], ["cc.<PERSON><PERSON>", ["_N$transition", "node", "clickEvents"], 2, 1, 9], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1]], [[4, 0, 1, 2, 2], [2, 0, 6, 2, 3, 4, 5, 2], [0, 0, 2, 3, 4, 2], [8, 0, 1, 2, 2], [9, 0, 1, 2, 3], [5, 0, 2], [2, 0, 7, 2, 3, 4, 5, 2], [2, 0, 1, 6, 2, 3, 8, 4, 3], [6, 0, 1, 2, 3, 4, 5, 2], [3, 0, 3, 2], [3, 0, 1, 2, 3, 4], [7, 0, 1, 1], [4, 1, 2, 1], [0, 0, 1, 2, 3, 4, 3], [0, 2, 3, 4, 1], [0, 2, 3, 1]], [[[[5, "FailUI"], [6, "fail", [-5, -6, -7, -8, -9], [[9, 45, -2], [11, -4, -3]], [12, -1, 0], [5, 750, 1334], [375, 667, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "bg", 125, 1, [[13, 1, 0, -10, [0], 1], [10, 45, 40, 36, -11]], [0, "d1CikS+0NLmr9RqP6zzCaj", 1, 0], [4, 4278190080], [5, 750, 1334]], [1, "btn_start", 1, [[2, 1, -12, [2], 3], [3, 3, -13, [[4, "9a23242ZdZNUZiEOZr1iRnK", "onClickStartGame", 1]]]], [0, "46shVZuHVGvajKPYHBxOPZ", 1, 0], [5, 243, 89], [170, -360, 0, 0, 0, 0, 1, 1, 1, 0]], [1, "btn_start", 1, [[2, 1, -14, [4], 5], [3, 3, -15, [[4, "9a23242ZdZNUZiEOZr1iRnK", "onClickShare", 1]]]], [0, "b7Js58mzFAsaY4uS2EtAVV", 1, 0], [5, 243, 89], [-170, -360, 0, 0, 0, 0, 1, 1, 1, 0]], [1, "game_fail", 1, [[14, -16, [6], 7]], [0, "768N/wL5hJX7BNCAIAIiKN", 1, 0], [5, 483, 133], [0, 392.25, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "cat_skin_1", 1, [-17], [0, "a3Fk2r0RVDGKHQ2uzXtOcc", 1, 0], [5, 48, 86], [0, 0, 0, 0, 0, 0, 1, 3, 3, 1]], [15, 6, [8]]], 0, [0, 3, 1, 0, 0, 1, 0, 4, 7, 0, 0, 1, 0, -1, 2, 0, -2, 3, 0, -3, 4, 0, -4, 5, 0, -5, 6, 0, 0, 2, 0, 0, 2, 0, 0, 3, 0, 0, 3, 0, 0, 4, 0, 0, 4, 0, 0, 5, 0, -1, 7, 0, 5, 1, 17], [0, 0, 0, 0, 0, 0, 0, 0, 0, 7], [-1, 1, -1, 1, -1, 1, -1, 1, -1, 1], [0, 1, 0, 2, 0, 3, 0, 4, 0, 5]], [[{"name": "btn_restart", "rect": [2, 2, 243, 89], "offset": [-0.5, 0.5], "originalSize": [248, 94], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [2], [6]], [[{"name": "btn_share_1", "rect": [2, 2, 243, 89], "offset": [-0.5, 0.5], "originalSize": [248, 94], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [2], [7]], [[{"name": "game_fail", "rect": [2, 1, 483, 133], "offset": [-0.5, 0.5], "originalSize": [488, 136], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [2], [8]]]]