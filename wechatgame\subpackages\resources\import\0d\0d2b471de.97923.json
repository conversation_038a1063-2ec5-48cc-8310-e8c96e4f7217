[1, ["ecpdLyjvZBwrvm+cedCcQy", "a2MjXRFdtLlYQ5ouAFv/+R", "37z4KFT8hJqK7j9lzVqwFV", "a1aUTOq01F55f08IzFAozc", "eddLYKczlMComzr6Ud8KQa", "fdokTAvxxA75S5291yVAf9", "63/VFBADRLrbj8rDUzqncu"], ["node", "_spriteFrame", "root", "data", "_textureSetter"], [["cc.Node", ["_name", "_opacity", "_components", "_prefab", "_contentSize", "_trs", "_parent", "_children", "_color"], 1, 9, 4, 5, 7, 1, 2, 5], ["cc.Sprite", ["_sizeMode", "_type", "node", "_materials", "_spriteFrame"], 1, 1, 3, 6], ["cc.Widget", ["_alignFlags", "_originalWidth", "_originalHeight", "node"], 0, 1], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.Label", ["_string", "_styleFlags", "_N$horizontalAlign", "_N$verticalAlign", "_fontSize", "_lineHeight", "node", "_materials"], -3, 1, 3], "cc.SpriteFrame", ["cc.Prefab", ["_name"], 2], ["e0baaQDeTZFIImqHCYusgcT", ["node"], 3, 1], ["cc.BlockInputEvents", ["node"], 3, 1], ["cc.<PERSON><PERSON>", ["_N$transition", "node", "clickEvents"], 2, 1, 9], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1]], [[3, 0, 1, 2, 2], [0, 0, 6, 2, 3, 4, 5, 2], [1, 2, 3, 4, 1], [9, 0, 1, 2, 2], [10, 0, 1, 2, 3], [6, 0, 2], [0, 0, 7, 2, 3, 4, 5, 2], [0, 0, 6, 7, 2, 3, 4, 5, 2], [0, 0, 1, 6, 2, 3, 8, 4, 3], [0, 0, 6, 2, 3, 8, 4, 5, 2], [2, 0, 3, 2], [2, 0, 1, 2, 3, 4], [7, 0, 1], [8, 0, 1], [3, 1, 2, 1], [1, 0, 2, 3, 4, 2], [1, 1, 0, 2, 3, 4, 3], [4, 0, 4, 5, 1, 2, 3, 6, 7, 7], [4, 0, 1, 2, 3, 6, 7, 5]], [[[[5, "TimeOverUI"], [6, "TimeOverUI", [-5, -6], [[10, 45, -2], [12, -3], [13, -4]], [14, -1, 0], [5, 750, 1334], [375, 667, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "bg_step", 1, [-8, -9, -10, -11, -12], [[15, 0, -7, [10], 11]], [0, "caVMefSEVMIIT9kZ3f0yRd", 1, 0], [5, 624, 984], [0, 5, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "bg", 125, 1, [[16, 1, 0, -13, [0], 1], [11, 45, 40, 36, -14]], [0, "d1CikS+0NLmr9RqP6zzCaj", 1, 0], [4, 4278190080], [5, 750, 1334]], [1, "btn_add", 2, [[2, -15, [3], 4], [3, 3, -16, [[4, "e0baaQDeTZFIImqHCYusgcT", "onClickAdd", 1]]]], [0, "52omi46FlMjKTvCKPfGO8q", 1, 0], [5, 288, 96], [0, -151.879, 0, 0, 0, 0, 1, 1, 1, 0]], [1, "btn_no", 2, [[2, -17, [5], 6], [3, 3, -18, [[4, "e0baaQDeTZFIImqHCYusgcT", "onClickCancel", 1]]]], [0, "30n4BsETJNFpHJ341+QgGB", 1, 0], [5, 288, 96], [0, -277.294, 0, 0, 0, 0, 1, 1, 1, 0]], [1, "New Label", 2, [[17, "时间补给", 50, 50, 1, 1, 1, -19, [2]]], [0, "43AoGexOlE17n01SuLDN0h", 1, 0], [5, 200, 63], [0, 392.963, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "hand", 2, [[2, -20, [7], 8]], [0, "8e19qVZ6tPY5a05lIlEMKU", 1, 0], [5, 129, 139], [0, 158.627, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "New Label", 2, [[18, "是否补充时间 60 秒", 1, 1, 1, -21, [9]]], [0, "077iGnrg5PWbqWTumSpKFE", 1, 0], [4, 4278210461], [5, 346.72, 50.4], [0, -5, 0, 0, 0, 0, 1, 1, 1, 1]]], 0, [0, 2, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, -1, 3, 0, -2, 2, 0, 0, 2, 0, -1, 6, 0, -2, 4, 0, -3, 5, 0, -4, 7, 0, -5, 8, 0, 0, 3, 0, 0, 3, 0, 0, 4, 0, 0, 4, 0, 0, 5, 0, 0, 5, 0, 0, 6, 0, 0, 7, 0, 0, 8, 0, 3, 1, 21], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [-1, 1, -1, -1, 1, -1, 1, -1, 1, -1, -1, 1], [0, 1, 0, 0, 2, 0, 3, 0, 4, 0, 0, 5]], [[{"name": "<PERSON><PERSON><PERSON><PERSON>", "rect": [7, 2, 129, 139], "offset": [-0.5, 0.5], "originalSize": [144, 144], "capInsets": [0, 0, 0, 0]}], [5], 0, [0], [4], [6]]]]