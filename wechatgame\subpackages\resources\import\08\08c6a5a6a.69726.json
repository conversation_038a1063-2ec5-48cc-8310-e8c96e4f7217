[1, ["ecpdLyjvZBwrvm+cedCcQy", "dfhIDfPqVFDYiYFdJWrTr6", "1bpzEIMj5OULRAsfGD3mYX", "0fk54LYI1AsLfsOK6wXZRI", "2cdpG5DH1N6o+FeG4hyp+R", "e97GVMl6JHh5Ml5qEDdSGa", "f0BIwQ8D5Ml7nTNQbh1YlS", "29FYIk+N1GYaeWH/q1NxQO", "bbha45EixIH6++essznxC9", "3eymoFvFRMr70YDdMGL+Yc", "f0UK+22itCt5YFPXweluOI", "62PCxpHUpEK6p9SIYQb8Np", "65CnGtBVVMWp6l9PeqpacM", "a6ZGcTAhBNMZtCSIiBUYjs", "af4+xVzlNEn7D0Y/QSwGe0", "ba8kr62XhGsbtsGwdawIxE", "aaoisHw0tDI7jQPtcXu5Tk", "8cKA0KFWlDbp4UQXAy08tF", "89aSvgSLtJoYbDSHPHQ/Ot", "755Pa62HNLlbUpWtanAyWP", "f35WwROg5PjbHfHZpwdhyH"], ["node", "_spriteFrame", "_textureSetter", "root", "btn_conatiner", "_N$target", "data", "_parent", "_N$normalSprite", "_N$pressedSprite", "_N$hoverSprite", "_N$disabledSprite"], [["cc.Node", ["_name", "_active", "_obj<PERSON><PERSON>s", "_opacity", "_components", "_prefab", "_contentSize", "_parent", "_trs", "_children"], -1, 9, 4, 5, 1, 7, 2], "cc.SpriteFrame", ["cc.Widget", ["_alignFlags", "_originalWidth", "_originalHeight", "_right", "_top", "_isAbsRight", "_isAbsTop", "_left", "_bottom", "_isAbsLeft", "_isAbsBottom", "alignMode", "node"], -9, 1], ["cc.Sprite", ["_type", "_sizeMode", "node", "_materials", "_spriteFrame"], 1, 1, 3, 6], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.<PERSON><PERSON>", ["_N$transition", "node", "clickEvents", "_N$normalColor", "_N$pressedColor", "_N$disabledColor", "_N$target", "_N$normalSprite", "_N$pressedSprite", "_N$hoverSprite", "_N$disabledSprite"], 2, 1, 9, 5, 5, 5, 1, 6, 6, 6, 6], ["cc.ClickEvent", ["_componentId", "handler", "customEventData", "target"], 0, 1], ["cc.Prefab", ["_name"], 2], ["58eefbmOaBF2aw6qPwjcAfL", ["node", "btn_conatiner"], 3, 1, 1], ["cc.Layout", ["_resize", "_N$layoutType", "node", "_layoutSize"], 1, 1, 5], ["cc.BlockInputEvents", ["node"], 3, 1], ["cc.Label", ["_string", "_fontSize", "_lineHeight", "_styleFlags", "_N$horizontalAlign", "_N$verticalAlign", "node", "_materials"], -3, 1, 3]], [[4, 0, 1, 2, 2], [3, 2, 3, 4, 1], [0, 0, 7, 9, 4, 5, 6, 8, 2], [2, 0, 12, 2], [2, 0, 1, 2, 12, 4], [0, 0, 1, 7, 4, 5, 6, 3], [0, 0, 1, 7, 4, 5, 6, 8, 3], [5, 1, 2, 1], [6, 0, 1, 2, 3, 4], [0, 0, 9, 4, 5, 6, 8, 2], [0, 0, 7, 4, 5, 6, 8, 2], [3, 0, 1, 2, 3, 4, 3], [7, 0, 2], [0, 0, 7, 9, 4, 5, 6, 2], [0, 0, 2, 3, 7, 4, 5, 6, 4], [2, 0, 7, 3, 4, 8, 9, 5, 6, 10, 1, 2, 12, 12], [2, 0, 3, 5, 12, 4], [2, 11, 0, 1, 2, 12, 5], [2, 0, 4, 6, 12, 4], [8, 0, 1, 1], [4, 1, 2, 1], [9, 0, 1, 2, 3, 3], [3, 0, 2, 3, 4, 2], [5, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 2], [6, 0, 1, 3, 3], [10, 0, 1], [11, 0, 1, 2, 3, 4, 5, 6, 7, 7]], [[[[12, "ChangeGameBgUI"], [9, "view", [-5, -6], [[3, 45, -2], [19, -4, -3]], [20, -1, 0], [5, 750, 1334], [375, 667, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "container", [-9, -10, -11, -12, -13, -14], [[21, 1, 3, -7, [5, 561.6, 492]], [15, 45, 0.05, 0.05, 0.22295096021947874, 0.10215192043895746, false, false, false, false, 300, 1556, -8]], [0, "10Hol4ihRNOrFhWjG2l8+K", 1, 0], [5, 561.6, 492], [0, -44.03125, 0, 0, 0, 0, 1, 1, 1, 1]], [13, "New Sprite", 1, [-16, -17, 2], [[22, 1, -15, [45], 46]], [0, "3cvqNZYbJCOoFbB0qRteJ2", 1, 0], [5, 624, 729]], [2, "bg1", 2, [-20, -21], [[7, -18, [[8, "58eefbmOaBF2aw6qPwjcAfL", "onClickChangeBgBtn", "bg1", 1]]], [1, -19, [13], 14]], [0, "f07P1XUENA3Kkw5RE00OGd", 1, 0], [5, 184, 246], [-188.8, 123, 0, 0, 0, 0, 1, 0.8, 0.8, 1]], [2, "bg2", 2, [-24, -25], [[7, -22, [[8, "58eefbmOaBF2aw6qPwjcAfL", "onClickChangeBgBtn", "bg2", 1]]], [1, -23, [19], 20]], [0, "a2slGFdrpEcI1sly7TvU/G", 1, 0], [5, 184, 246], [-4.800000000000011, 123, 0, 0, 0, 0, 1, 0.8, 0.8, 1]], [2, "bg3", 2, [-28, -29], [[7, -26, [[8, "58eefbmOaBF2aw6qPwjcAfL", "onClickChangeBgBtn", "bg3", 1]]], [1, -27, [25], 26]], [0, "cbzVIqS9JLC63Tjfn9bzil", 1, 0], [5, 184, 246], [179.2, 123, 0, 0, 0, 0, 1, 0.8, 0.8, 1]], [2, "bg4", 2, [-32, -33], [[7, -30, [[8, "58eefbmOaBF2aw6qPwjcAfL", "onClickChangeBgBtn", "bg4", 1]]], [1, -31, [31], 32]], [0, "9b0BqZwYFNuLJm6i+1nW1R", 1, 0], [5, 184, 246], [-188.8, -123, 0, 0, 0, 0, 1, 0.8, 0.8, 1]], [2, "bg5", 2, [-36, -37], [[7, -34, [[8, "58eefbmOaBF2aw6qPwjcAfL", "onClickChangeBgBtn", "bg5", 1]]], [1, -35, [37], 38]], [0, "d7nPfy6jtCv7Fd05uLmLNa", 1, 0], [5, 184, 246], [-4.800000000000011, -123, 0, 0, 0, 0, 1, 0.8, 0.8, 1]], [2, "bg6", 2, [-40, -41], [[7, -38, [[8, "58eefbmOaBF2aw6qPwjcAfL", "onClickChangeBgBtn", "bg6", 1]]], [1, -39, [43], 44]], [0, "aaqnOqhRRDPqNwcPiTLALH", 1, 0], [5, 184, 246], [179.2, -123, 0, 0, 0, 0, 1, 0.8, 0.8, 1]], [14, "bg", 512, 200, 1, [[11, 1, 0, -42, [0], 1], [4, 45, 100, 100, -43], [25, -44]], [0, "a42tBIu8ZDU5XRzOJjjGC0", 1, 0], [5, 750, 1334]], [2, "cancel", 3, [-48], [[23, 3, -46, [[24, "58eefbmOaBF2aw6qPwjcAfL", "onClickClose", 1]], [4, 4293322470], [4, 4291348680], [4, 3363338360], -45, 5, 6, 7, 8], [16, 33, -0.02, false, -47]], [0, "69KnMtBetINYuEZYRmB6ru", 1, 0], [5, 80, 80], [284.48, 324.5, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "Background", 11, [[11, 1, 0, -49, [3], 4], [17, 0, 45, 100, 40, -50]], [0, "968MZs07RIGZn+04Je+2lC", 1, 0], [5, 80, 80], [0, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [10, "New Label", 3, [[26, "更换背景", 50, 60, 1, 1, 1, -51, [2]], [18, 17, 0.04, false, -52]], [0, "7b864NwoJK+p9mLcAi7Nuf", 1, 0], [5, 200, 75.6], [0, 297.53999999999996, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "frame", false, 4, [[1, -53, [9], 10], [4, 45, 184, 246, -54]], [0, "30ZBUrYbBPtp/r9Vrs1st3", 1, 0], [5, 184, 246]], [6, "checkMark", false, 4, [[1, -55, [11], 12], [3, 36, -56]], [0, "41La1eiilNmrIR5GGV/FXA", 1, 0], [5, 123, 125], [48.95, -79.25, 0, 0, 0, 0, 1, 0.7, 0.7, 1]], [5, "frame", false, 5, [[1, -57, [15], 16], [4, 45, 184, 246, -58]], [0, "34MSAt5yhHboRR1JM6QWN2", 1, 0], [5, 184, 246]], [6, "checkMark", false, 5, [[1, -59, [17], 18], [3, 36, -60]], [0, "17nLEhykpEPKYRhIxwlHVM", 1, 0], [5, 123, 125], [48.95, -79.25, 0, 0, 0, 0, 1, 0.7, 0.7, 1]], [5, "frame", false, 6, [[1, -61, [21], 22], [4, 45, 184, 246, -62]], [0, "6dUtAncktCYb/TqEkGjz7Y", 1, 0], [5, 184, 246]], [6, "checkMark", false, 6, [[1, -63, [23], 24], [3, 36, -64]], [0, "fdBz6+b0JERoQlH1X7wtZ2", 1, 0], [5, 123, 125], [48.95, -79.25, 0, 0, 0, 0, 1, 0.7, 0.7, 1]], [5, "frame", false, 7, [[1, -65, [27], 28], [4, 45, 184, 246, -66]], [0, "f7PumbhFdIi4XXbnd/mkIz", 1, 0], [5, 184, 246]], [6, "checkMark", false, 7, [[1, -67, [29], 30], [3, 36, -68]], [0, "05tsmN4eNJ9KXL/+X6S7x3", 1, 0], [5, 123, 125], [48.95, -79.25, 0, 0, 0, 0, 1, 0.7, 0.7, 1]], [5, "frame", false, 8, [[1, -69, [33], 34], [4, 45, 184, 246, -70]], [0, "8dbIQGx71LBJCecRhPznCZ", 1, 0], [5, 184, 246]], [6, "checkMark", false, 8, [[1, -71, [35], 36], [3, 36, -72]], [0, "a6K0wjVs1H/rLeBYuwYwfe", 1, 0], [5, 123, 125], [48.95, -79.25, 0, 0, 0, 0, 1, 0.7, 0.7, 1]], [5, "frame", false, 9, [[1, -73, [39], 40], [4, 45, 184, 246, -74]], [0, "3eJXW7CC1EPryX3dye80OQ", 1, 0], [5, 184, 246]], [6, "checkMark", false, 9, [[1, -75, [41], 42], [3, 36, -76]], [0, "40Cq4QvhpD278UDdG4ZrvI", 1, 0], [5, 123, 125], [48.95, -79.25, 0, 0, 0, 0, 1, 0.7, 0.7, 1]]], 0, [0, 3, 1, 0, 0, 1, 0, 4, 2, 0, 0, 1, 0, -1, 10, 0, -2, 3, 0, 0, 2, 0, 0, 2, 0, -1, 4, 0, -2, 5, 0, -3, 6, 0, -4, 7, 0, -5, 8, 0, -6, 9, 0, 0, 3, 0, -1, 13, 0, -2, 11, 0, 0, 4, 0, 0, 4, 0, -1, 14, 0, -2, 15, 0, 0, 5, 0, 0, 5, 0, -1, 16, 0, -2, 17, 0, 0, 6, 0, 0, 6, 0, -1, 18, 0, -2, 19, 0, 0, 7, 0, 0, 7, 0, -1, 20, 0, -2, 21, 0, 0, 8, 0, 0, 8, 0, -1, 22, 0, -2, 23, 0, 0, 9, 0, 0, 9, 0, -1, 24, 0, -2, 25, 0, 0, 10, 0, 0, 10, 0, 0, 10, 0, 5, 12, 0, 0, 11, 0, 0, 11, 0, -1, 12, 0, 0, 12, 0, 0, 12, 0, 0, 13, 0, 0, 13, 0, 0, 14, 0, 0, 14, 0, 0, 15, 0, 0, 15, 0, 0, 16, 0, 0, 16, 0, 0, 17, 0, 0, 17, 0, 0, 18, 0, 0, 18, 0, 0, 19, 0, 0, 19, 0, 0, 20, 0, 0, 20, 0, 0, 21, 0, 0, 21, 0, 0, 22, 0, 0, 22, 0, 0, 23, 0, 0, 23, 0, 0, 24, 0, 0, 24, 0, 0, 25, 0, 0, 25, 0, 6, 1, 2, 7, 3, 76], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [-1, 1, -1, -1, 1, 8, 9, 10, 11, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1], [0, 4, 0, 0, 3, 3, 5, 6, 7, 0, 1, 0, 2, 0, 8, 0, 1, 0, 2, 0, 9, 0, 1, 0, 2, 0, 10, 0, 1, 0, 2, 0, 11, 0, 1, 0, 2, 0, 12, 0, 1, 0, 2, 0, 13, 0, 14]], [[{"name": "bg2", "rect": [0, 0, 184, 246], "offset": [0, 0], "originalSize": [184, 246], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [2], [15]], [[{"name": "bg4", "rect": [0, 0, 184, 246], "offset": [0, 0], "originalSize": [184, 246], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [2], [16]], [[{"name": "bg5", "rect": [0, 0, 184, 246], "offset": [0, 0], "originalSize": [184, 246], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [2], [17]], [[{"name": "bg6", "rect": [0, 0, 184, 246], "offset": [0, 0], "originalSize": [184, 246], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [2], [18]], [[{"name": "bg1", "rect": [0, 0, 184, 246], "offset": [0, 0], "originalSize": [184, 246], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [2], [19]], [[{"name": "bg3", "rect": [0, 0, 184, 246], "offset": [0, 0], "originalSize": [184, 246], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [2], [20]]]]