[1, ["ecpdLyjvZBwrvm+cedCcQy", "ddx2VI4TVAB6EWXCEBOK5+", "2cdpG5DH1N6o+FeG4hyp+R", "d2ZQcBqTdBcbldudYvIl54", "eeH9v55IpDaq6UXt4068CA", "c6Kyw68wNJ4aNafZ+qDpqy", "f0mZa5dgdJra+kWDLYa2w2", "f8+Ii9VwxFj6kVis7K3OLx", "82YUh8M35NBbMyDtIrqmuf", "3fy7HEYodKtZEnIYS1cUJz", "5ekNbqLCJFMKrDGWgde8VE", "15BF0RprlAF7nxru54kEmE", "8abSOlkzlKeqOU62GZq2e3", "a9S1lcg+ZF6rWbPfQf0uQQ", "79gUgUWbdIf6fK0XgMaQAe", "dfYOtkW79Dkqv8/u39gAab", "a4TVumROJHdLCW2jZmFmqL", "3emu+SXJhBX6qqQ6IYRNih", "b6hKVym8lJN4GIp+37zeh6", "99a9tzjfFPTZL9ZG9JL1+b", "74g/xPpMhDz7Ue6hJaVIJE", "9bKenfEOhEb5uv3VzksnbA", "75teQqt5NMXbjcJMltUu9G", "2bq0v9iypLf7Cw7y4QDPjM", "ebD7IVCHxGBoD1bWwsu2Vo", "c67ShmJctIh72oeRJ0CJsR", "ad837/EAdJgKBwDUqwiIzY"], ["node", "_spriteFrame", "_textureSetter", "_parent", "root", "<PERSON><PERSON><PERSON><PERSON>", "curLevel", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "selectNode", "buttonsNode", "weilanNode", "maskNode", "gameBg", "showNode", "cat<PERSON>ontainer", "data"], [["cc.Node", ["_name", "_active", "_opacity", "_obj<PERSON><PERSON>s", "_prefab", "_parent", "_contentSize", "_components", "_trs", "_children", "_eulerAngles", "_color"], -1, 4, 1, 5, 9, 7, 2, 5, 5], "cc.SpriteFrame", ["cc.Widget", ["_alignFlags", "_bottom", "_top", "_originalWidth", "_originalHeight", "node", "_target"], -2, 1, 1], ["cc.Sprite", ["_sizeMode", "_type", "node", "_materials", "_spriteFrame"], 1, 1, 3, 6], ["cc.<PERSON><PERSON>", ["_N$transition", "node", "clickEvents"], 2, 1, 9], ["cc.Label", ["_string", "_N$horizontalAlign", "_N$verticalAlign", "_styleFlags", "_fontSize", "_lineHeight", "node", "_materials"], -3, 1, 3], ["cc.Node", ["_name", "_obj<PERSON><PERSON>s", "_parent", "_components", "_prefab", "_contentSize", "_trs"], 1, 1, 12, 4, 5, 7], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_parent", "_children", "_components", "_prefab", "_contentSize", "_trs"], 2, 1, 9, 9, 4, 5, 7], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_contentSize", "_trs"], 2, 1, 2, 4, 5, 7], ["5e091tclyBN4rfjM7tfE2sT", ["node", "gameBackgroundArray", "cat<PERSON>ontainer", "showNode", "gameBg", "maskNode", "weilanNode", "buttonsNode", "selectNode", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "curLevel", "<PERSON><PERSON><PERSON><PERSON>"], 3, 1, 3, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], ["cc.Layout", ["_resize", "_N$layoutType", "_N$paddingLeft", "_N$paddingRight", "_N$spacingX", "node"], -2, 1], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1], ["cc.BlockInputEvents", ["node"], 3, 1], ["cc.LabelOutline", ["_width", "node", "_color"], 2, 1, 5]], [[7, 0, 1, 2, 2], [3, 2, 3, 4, 1], [0, 0, 5, 7, 4, 6, 8, 2], [13, 0, 1, 2, 3], [0, 0, 5, 9, 7, 4, 6, 8, 2], [4, 0, 1, 2, 2], [2, 0, 2, 5, 6, 3], [0, 0, 5, 4, 6, 8, 2], [2, 0, 1, 5, 6, 3], [10, 0, 1, 2, 3, 4, 5, 2], [2, 0, 5, 2], [3, 1, 0, 2, 3, 4, 3], [14, 0, 1], [15, 0, 1, 2, 2], [5, 0, 4, 5, 1, 2, 6, 7, 6], [8, 0, 2], [0, 0, 9, 7, 4, 6, 8, 2], [0, 0, 5, 9, 4, 8, 2], [0, 0, 1, 5, 9, 4, 3], [0, 0, 1, 2, 7, 4, 6, 8, 10, 4], [0, 0, 1, 5, 9, 7, 4, 6, 8, 3], [0, 0, 2, 5, 7, 4, 6, 3], [0, 0, 3, 1, 2, 5, 7, 4, 11, 6, 5], [0, 0, 1, 5, 7, 4, 6, 3], [0, 0, 3, 5, 9, 4, 8, 10, 3], [9, 0, 1, 2, 3, 4, 5, 6, 2], [6, 0, 2, 3, 4, 5, 6, 2], [6, 0, 1, 2, 3, 4, 5, 3], [2, 0, 3, 4, 5, 4], [11, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 1], [7, 1, 2, 1], [12, 0, 1, 2, 3, 4, 5, 6], [3, 0, 2, 3, 4, 2], [3, 0, 2, 3, 2], [4, 1, 1], [4, 1, 2, 1], [5, 0, 3, 1, 2, 6, 7, 5], [5, 0, 4, 5, 3, 1, 2, 6, 7, 7]], [[[{"name": "btn_add_time", "rect": [1, 1, 120, 120], "offset": [0, 0], "originalSize": [122, 122], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [2], [4]], [[{"name": "btn_chehui", "rect": [2, 2, 118, 118], "offset": [0, 0], "originalSize": [122, 122], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [2], [5]], [[{"name": "btn_tips", "rect": [2, 2, 118, 118], "offset": [0, 0], "originalSize": [122, 122], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [2], [6]], [[{"name": "info_time", "rect": [2, 1, 180, 66], "offset": [0.5, 0], "originalSize": [183, 68], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [2], [7]], [[{"name": "btn_direction", "rect": [2, 2, 118, 118], "offset": [0, 0], "originalSize": [122, 122], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [2], [8]], [[[15, "GameUI - 001"], [16, "view", [-14, -15, -16, -17, -18, -19, -20, -21, -22, -23, -24], [[10, 45, -2], [29, -13, [43, 44, 45, 46, 47, 48], -12, -11, -10, -9, -8, -7, -6, -5, -4, -3]], [30, -1, 0], [5, 750, 1334], [375, 667, 0, 0, 0, 0, 1, 1, 1, 1]], [17, "New Node", 1, [-25, -26, -27, -28, -29, -30, -31, -32], [0, "55njfknkhJV7/JJriLYJu7", 1, 0], [0, -581.833, 0, 0, 0, 0, 1, 1, 1, 1]], [25, "Top container", 1, [[7, "left", -35, [0, "femmWJ5ZBGsZGJ0wzLwFBm", 1, 0], [5, 86.66666666666667, 0], [-96.66666666666666, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "center", -36, [0, "adUMLZglJIdYvp1018C/EB", 1, 0], [5, 86.66666666666667, 0], [1.4210854715202004e-14, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "right", -37, [0, "7aP6HU1dRGbYtGYhjvvJoI", 1, 0], [5, 86.66666666666667, 0], [96.66666666666669, 0, 0, 0, 0, 0, 1, 1, 1, 1]]], [[10, 17, -33], [31, 1, 1, 10, 10, 10, -34]], [0, "cc7aG0qQtIcZM3K/jN8+1N", 1, 0], [5, 300, 200], [0, 567, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "btn_ch", 2, [-41], [[1, -38, [12], 13], [5, 3, -39, [[3, "5e091tclyBN4rfjM7tfE2sT", "onClickRevoke", 1]]], [8, 4, 58.45100000000005, -40, 1]], [0, "5eI65lo+FDXbeqvZU3pQtg", 1, 0], [5, 118, 118], [-212.478, 32.28399999999999, 0, 0, 0, 0, 1, 1, 1, 0]], [4, "btn_ts", 2, [-45], [[1, -42, [20], 21], [5, 3, -43, [[3, "5e091tclyBN4rfjM7tfE2sT", "onClickLightCat", 1]]], [8, 4, 58.45100000000008, -44, 1]], [0, "22AOgUPG5O9K3YVpYfwDbU", 1, 0], [5, 118, 118], [75.297, 32.28399999999999, 0, 0, 0, 0, 1, 1, 1, 0]], [4, "btn_fz", 2, [-49], [[1, -46, [24], 25], [5, 3, -47, [[3, "5e091tclyBN4rfjM7tfE2sT", "onClickResetDirection", 1]]], [8, 4, 61.24000000000002, -48, 1]], [0, "4bPh4JdTJLeo67jq68PYJC", 1, 0], [5, 118, 118], [226, 35.07299999999998, 0, 0, 0, 0, 1, 1, 1, 0]], [18, "choose", false, 1, [-50, -51], [0, "f4d52+ZwpBl7I9ssihhO1H", 1, 0]], [19, "maskNode", false, 100, [[11, 1, 0, -52, [8], 9], [12, -53]], [0, "c5QsVlQbpBqaJfgNa4u90B", 1, 0], [5, 2000, 3000], [0, 0, 0, 0, 0, -0.3826834323650898, 0.9238795325112867, 1, 1, 1], [1, 0, 0, -45]], [4, "btn_js", 2, [-56], [[1, -54, [16], 17], [5, 3, -55, [[3, "5e091tclyBN4rfjM7tfE2sT", "onClickAddTime", 1]]]], [0, "b7TltzZf5KCL5zEmu+GxN0", 1, 0], [5, 120, 120], [-63.651, 32.284, 0, 0, 0, 0, 1, 1, 1, 0]], [2, "btn_zt", 2, [[1, -57, [26], 27], [5, 3, -58, [[3, "5e091tclyBN4rfjM7tfE2sT", "onClickPause", 1]]], [6, 1, 70.5440000000001, -59, 1]], [0, "bbu6pnbKxJ3IN3oddCbq7j", 1, 0], [5, 88, 88], [-309.724, 1134.2889999999998, 0, 0, 0, 0, 1, 1, 1, 0]], [26, "New Label", 1, [[-60, [13, 1.5, -61, [4, 4285101568]], [6, 1, 88.60000000000002, -62, 1]], 1, 4, 4], [0, "e9/VzD3OtEz7fD4Ix5SStn", 1, 0], [5, 113.04, 40.8], [0, 558, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "info_step", 1, [-65], [[1, -63, [38], 39], [6, 1, 75.5, -64, 1]], [0, "11ZUPpIHhDsrcGj+dPrFS+", 1, 0], [5, 181, 67], [980.99, 558, 0, 0, 0, 0, 1, 1, 1, 1]], [20, "info_time", false, 1, [-68], [[1, -66, [41], 42], [6, 1, 352.28, -67, 1]], [0, "d8u1wgngpB7bNRqngWhDpQ", 1, 0], [5, 180, 66], [-180, 281.72, 0, 0, 0, 0, 1, 1, 1, 1]], [27, "bg", 512, 1, [[-69, [28, 45, 100, 100, -70]], 1, 4], [0, "82nHYmy7hNmLw5boeme2a6", 1, 0], [5, 750, 1334]], [21, "maskNode", 100, 7, [[11, 1, 0, -71, [1], 2], [12, -72]], [0, "83Jvr2Av5GRZiOUm+AEpHZ", 1, 0], [5, 2000, 3000]], [2, "New Label", 7, [[36, "请选择一个目标", 1, 1, 1, -73, [3]], [13, 2, -74, [4, 4278190080]]], [0, "9dY6MAhutAVr1k2ZklmQ1X", 1, 0], [5, 284, 54.4], [2.842170943040401e-14, -580, 0, 0, 0, 0, 1, 1, 1, 1]], [22, "showNode", 512, false, 36, 1, [[32, 0, -75, [4], 5]], [0, "e94jhtgdtPVZ+a748OAUhy", 1, 0], [4, 4287664272], [5, 660, 920]], [23, "weilan", false, 1, [[1, -76, [6], 7]], [0, "e6dWWIUQtOS6+sLwswGVxX", 1, 0], [5, 526, 772]], [24, "rogn<PERSON>", 512, 1, [8], [0, "19A34AJN1JroOJMh+8sP14", 1, 0], [0, 0, 0, 0, 0, 0.3826834323650898, 0.9238795325112867, 1, 1, 1], [1, 0, 0, 45]], [2, "btn_fluctuate", 2, [[1, -77, [28], 29], [34, -78]], [0, "bawZS+zXVHYIb3DW4Rhefn", 1, 0], [5, 88, 88], [-195.35, 1134.289, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "btn_change_bg", 2, [[1, -79, [32], 33], [35, -80, [[3, "5e091tclyBN4rfjM7tfE2sT", "onClickChangeBgBtn", 1]]]], [0, "e8yt0vmTdCDIPsZ0y9l8hs", 1, 0], [5, 88, 88], [183.94, 1133.685, 0, 0, 0, 0, 1, 1, 1, 1]], [33, 0, 14, [0]], [2, "8", 4, [[1, -81, [10], 11]], [0, "3dwR7RO1pDXL5ycTPpkGsC", 1, 0], [5, 66, 51], [34.111, 37.977, 0, 0, 0, 0, 1, 0.5, 0.5, 1]], [2, "8", 9, [[1, -82, [14], 15]], [0, "29kvNV4ZVC1L1AsN6KrInc", 1, 0], [5, 66, 51], [34.111, 37.977, 0, 0, 0, 0, 1, 0.5, 0.5, 1]], [2, "8", 5, [[1, -83, [18], 19]], [0, "95EjiO+GFFtZ66Ed6+4N0U", 1, 0], [5, 66, 51], [34.111, 37.977, 0, 0, 0, 0, 1, 0.5, 0.5, 1]], [2, "8", 6, [[1, -84, [22], 23]], [0, "38tavNZ+NO976knVsASoAe", 1, 0], [5, 66, 51], [34.111, 37.977, 0, 0, 0, 0, 1, 0.5, 0.5, 1]], [2, "8 copy", 2, [[1, -85, [30], 31]], [0, "6cjIqjX/5CX73E7xm58t+3", 1, 0], [5, 66, 51], [-187.999, 1140.501, 0, 0, 0, 0, 1, 0.5, 0.5, 1]], [2, "level_bg", 1, [[1, -86, [34], 35]], [0, "a5NXKjSIBHsJ4wYe6Pyauj", 1, 0], [5, 190, 66], [0, 559.29, 0, 0, 0, 0, 1, 1, 1, 1]], [37, "第 34 关", 30, 30, 1, 1, 1, 11, [36]], [9, "step", 12, [-87], [0, "0ejrdvyVlINK4HnguTqQWw", 1, 0], [5, 75.07, 37.8], [25, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [14, "12:12", 30, 30, 1, 1, 30, [37]], [9, "New Label", 13, [-88], [0, "43laoh5vpPv7V5hxILUeor", 1, 0], [5, 75.07, 37.8], [25, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [14, "12:12", 30, 30, 1, 1, 32, [40]]], 0, [0, 4, 1, 0, 0, 1, 0, 5, 31, 0, 6, 29, 0, 7, 33, 0, 8, 7, 0, 9, 2, 0, 10, 18, 0, 11, 8, 0, 12, 22, 0, 13, 17, 0, 14, 19, 0, 0, 1, 0, -1, 14, 0, -2, 3, 0, -3, 7, 0, -4, 17, 0, -5, 18, 0, -6, 19, 0, -7, 2, 0, -8, 28, 0, -9, 11, 0, -10, 12, 0, -11, 13, 0, -1, 4, 0, -2, 9, 0, -3, 5, 0, -4, 6, 0, -5, 10, 0, -6, 20, 0, -7, 27, 0, -8, 21, 0, 0, 3, 0, 0, 3, 0, 3, 3, 0, 3, 3, 0, 3, 3, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, -1, 23, 0, 0, 5, 0, 0, 5, 0, 0, 5, 0, -1, 25, 0, 0, 6, 0, 0, 6, 0, 0, 6, 0, -1, 26, 0, -1, 15, 0, -2, 16, 0, 0, 8, 0, 0, 8, 0, 0, 9, 0, 0, 9, 0, -1, 24, 0, 0, 10, 0, 0, 10, 0, 0, 10, 0, -1, 29, 0, 0, 11, 0, 0, 11, 0, 0, 12, 0, 0, 12, 0, -1, 30, 0, 0, 13, 0, 0, 13, 0, -1, 32, 0, -1, 22, 0, 0, 14, 0, 0, 15, 0, 0, 15, 0, 0, 16, 0, 0, 16, 0, 0, 17, 0, 0, 18, 0, 0, 20, 0, 0, 20, 0, 0, 21, 0, 0, 21, 0, 0, 23, 0, 0, 24, 0, 0, 25, 0, 0, 26, 0, 0, 27, 0, 0, 28, 0, -1, 31, 0, -1, 33, 0, 15, 1, 8, 3, 19, 88], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 22], [-1, -1, 1, -1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, -1, -1, 1, -1, -1, 1, -1, -2, -3, -4, -5, -6, 1], [0, 0, 2, 0, 0, 2, 0, 9, 0, 2, 0, 1, 0, 10, 0, 1, 0, 11, 0, 1, 0, 12, 0, 1, 0, 13, 0, 14, 0, 15, 0, 1, 0, 16, 0, 17, 0, 0, 0, 18, 0, 0, 19, 20, 3, 21, 22, 23, 24, 3]], [[{"name": "info_step", "rect": [0, 1, 181, 67], "offset": [-0.5, -0.5], "originalSize": [182, 68], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [2], [25]], [[{"name": "8", "rect": [0, 0, 66, 51], "offset": [0, 0], "originalSize": [66, 51], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [2], [26]]]]