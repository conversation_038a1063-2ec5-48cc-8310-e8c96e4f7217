[1, ["ecpdLyjvZBwrvm+cedCcQy", "81irxz13RLR4Nh5yQ5mWQv", "87SEL5eoxORqQ1vibyX3ir", "90YEEMU5VEWYOkwlENbcGX", "b8YnYiY9VIuLqcZ9/OrMDk", "f5te+roMxK2I+zihAh2u27", "017OdRRxBBaaAmYcdkyKt/", "7ez1AR4DhLxqKYwx7bLuJS", "20mJAejWdG2LcA/iG1S9bO", "0acRojzpBKwpKaQLKrM7uj", "39ejL9eypKe66xc/9qfZmQ", "3alZoO1T5PUZjMv3PDTF2S", "305FDMKaRFKICP2MiD1nAC", "62URyYJtVJVI8uQfzoE2KJ", "52c6HewGxLVItEkmimB720", "5aFFxSvUxIq5T7WcOiKcbi", "61lLEwlpRL0aXX/ekFOx5W", "f37QhHYqlGDqqOdteSSyvd", "8bM2vfEDFCHJsoZrIeEv00", "990G4JyZRNJoHF/hidfmsO", "6aPJZJcBpMGr0zUOM6TR3e", "daAzVEKIBOFoYG1fDyPoVH", "266yRHbSJA65+l5UDfrjW5", "baDH9ifRBMfLsOMFrBQzab", "248FsQ2FtEn6PIFH0/12V9", "4fAxSY6MBEoaIH4ZXNFTM3"], ["node", "_spriteFrame", "_textureSetter", "_parent", "root", "scrollView", "provinceContainer", "data", "yardPre"], ["cc.SpriteFrame", ["cc.Widget", ["_alignFlags", "_left", "_bottom", "_originalWidth", "_isAbsBottom", "_isAbsLeft", "_right", "_originalHeight", "_isAbsRight", "_top", "_isAbsTop", "node"], -8, 1], ["cc.Node", ["_name", "_obj<PERSON><PERSON>s", "_prefab", "_components", "_contentSize", "_trs", "_parent", "_children", "_anchorPoint"], 1, 4, 9, 5, 7, 1, 2, 5], ["cc.Sprite", ["_sizeMode", "_type", "_enabled", "_fillType", "node", "_materials", "_spriteFrame"], -1, 1, 3, 6], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.Layout", ["_resize", "_N$layoutType", "_N$paddingLeft", "_N$paddingRight", "_N$paddingTop", "_N$paddingBottom", "_N$spacingX", "_N$spacingY", "node", "_layoutSize"], -5, 1, 5], ["cc.<PERSON><PERSON>", ["_N$transition", "node", "clickEvents"], 2, 1, 9], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_parent", "_children", "_components", "_prefab", "_contentSize"], 2, 1, 2, 12, 4, 5], ["cc.Node", ["_name", "_active", "_parent", "_children", "_prefab"], 1, 1, 9, 4], ["4cb83Bxzk1BN7SH9SyDkPgc", ["node", "provinceContainer", "scrollView", "yardPre"], 3, 1, 1, 1, 6], ["cc.Mask", ["node", "_materials"], 3, 1, 3], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1], ["<PERSON><PERSON>", ["horizontal", "elastic", "node", "_N$content"], 1, 1, 1]], [[4, 0, 1, 2, 2], [2, 0, 6, 3, 2, 4, 5, 2], [12, 0, 1, 2, 3], [3, 4, 5, 6, 1], [6, 0, 1, 2, 2], [1, 0, 1, 2, 5, 4, 11, 6], [1, 0, 1, 6, 2, 5, 8, 4, 11, 8], [1, 0, 3, 7, 11, 4], [1, 0, 2, 4, 11, 4], [1, 0, 1, 9, 5, 10, 11, 6], [3, 0, 4, 5, 6, 2], [7, 0, 2], [2, 0, 7, 3, 2, 4, 5, 2], [2, 0, 7, 3, 2, 4, 8, 5, 2], [2, 0, 6, 3, 2, 4, 8, 5, 2], [2, 0, 6, 7, 3, 2, 4, 5, 2], [2, 0, 1, 6, 3, 2, 4, 3], [2, 0, 6, 2, 5, 2], [8, 0, 1, 2, 3, 4, 5, 2], [9, 0, 1, 2, 3, 4, 3], [1, 0, 1, 6, 11, 4], [1, 0, 2, 3, 7, 11, 5], [1, 0, 3, 11, 3], [1, 0, 11, 2], [10, 0, 1, 2, 3, 1], [4, 1, 2, 1], [5, 0, 1, 8, 9, 3], [5, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 9], [3, 2, 0, 3, 4, 5, 6, 4], [3, 1, 0, 4, 5, 6, 3], [3, 1, 4, 5, 6, 2], [11, 0, 1, 1], [6, 1, 2, 1], [13, 0, 1, 2, 3, 3]], [[[{"name": "btn_start_game", "rect": [1, 5, 332, 103], "offset": [-0.5, 7], "originalSize": [335, 127], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [2]], [[{"name": "btn_my_skin", "rect": [2, 6, 146, 108], "offset": [0, -2], "originalSize": [150, 116], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [3]], [[[11, "HomeUI"], [12, "view", [-6, -7, -8, -9, -10, -11, -12, -13, -14, -15, -16, -17], [[20, 45, 1, -1, -2], [24, -5, -4, -3, 27]], [25, -1, 0], [5, 750, 1334], [376, 667, 0, 0, 0, 0, 1, 1, 1, 1]], [13, "content container", [-21, -22], [[26, 1, 2, -18, [5, 750, 1885]], [21, 45, -751.0999999999999, 564, 1885, -19], [28, false, 0, 1, -20, [6], 7]], [0, "80oLuDQupPq4vBbVkJhZRQ", 1, 0], [5, 750, 1885], [0, 0.5, 1], [0, 566.95, 0, 0, 0, 0, 1, 1, 1, 1]], [14, "content", 2, [[27, 1, 2, 11, 11, -40, 5, 5, 40, -23, [5, 750, 1500]], [22, 40, 580, -24], [29, 2, 0, -25, [4], 5]], [0, "0fa3MXkh9K2ZbS1IfvjL7X", 1, 0], [5, 750, 1500], [0, 0.5, 1], [0, -385, 0, 0, 0, 0, 1, 1, 1, 1]], [18, "scoll view container", 1, [-28], [[-26, [7, 45, 600, 600, -27]], 1, 4], [0, "e1GGxIfFBI0qDHmuB8+MRP", 1, 0], [5, 750, 1334]], [15, "view", 4, [2], [[31, -29, [8]], [8, 45, 0.15, false, -30]], [0, "542qP+UgZP6JhYhZJUp/j9", 1, 0], [5, 750, 1133.9], [0, 100.05000000000007, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "btn_start", 1, [[30, 1, -31, [9], 10], [4, 3, -32, [[2, "4cb83Bxzk1BN7SH9SyDkPgc", "onClickStartGame", 1]]], [8, 20, 0.05, false, -33]], [0, "2eDbYOWyRJcrANAsFrVL/M", 1, 0], [5, 332, 103], [0, -548.8, 0, 0, 0, 0, 1, 1, 1, 0]], [1, "rank", 1, [[3, -34, [11], 12], [4, 3, -35, [[2, "4cb83Bxzk1BN7SH9SyDkPgc", "onClickProvinceRank", 1]]], [5, 12, 0.03, 0.15, false, false, -36]], [0, "15xfBF7v1COKh0hQmTGKOX", 1, 0], [5, 146, 108], [-279.5, -412.9, 0, 0, 0, 0, 1, 1, 1, 0]], [1, "fenxiang", 1, [[3, -37, [13], 14], [4, 3, -38, [[2, "4cb83Bxzk1BN7SH9SyDkPgc", "onClickShare", 1]]], [6, 36, 0.03, 0.03, 0.05, false, false, false, -39]], [0, "27peI1s69PObunbgdnoOVS", 1, 0], [5, 146, 108], [279.5, -546.3, 0, 0, 0, 0, 1, 1, 1, 0]], [1, "shop", 1, [[3, -40, [15], 16], [4, 3, -41, [[2, "4cb83Bxzk1BN7SH9SyDkPgc", "onClickSkinGallery", 1]]], [6, 36, 0.03, 0.03, 0.25, false, false, false, -42]], [0, "d3jyQPLMNIe55Cpdqn+yPW", 1, 0], [5, 146, 108], [279.5, -279.5, 0, 0, 0, 0, 1, 1, 1, 0]], [1, "setting", 1, [[3, -43, [17], 18], [4, 3, -44, [[2, "4cb83Bxzk1BN7SH9SyDkPgc", "onClickSettingBtn", 1]]], [9, 9, 0.05, 0.05, false, false, -45]], [0, "0c3HUfto1Ol4jqU+nEM4BT", 1, 0], [5, 88, 88], [-293.5, 556.3, 0, 0, 0, 0, 1, 1, 1, 0]], [1, "more", 1, [[3, -46, [19], 20], [4, 3, -47, [[2, "4cb83Bxzk1BN7SH9SyDkPgc", "notifyComingSoon", 1]]], [5, 12, -0.8849066666666664, 0.25, false, false, -48]], [0, "0eXQwLeSVATK60UhbQq3i7", 1, 0], [5, 146, 108], [-965.6799999999998, -279.5, 0, 0, 0, 0, 1, 1, 1, 0]], [1, "position", 1, [[3, -49, [21], 22], [4, 3, -50, [[2, "4cb83Bxzk1BN7SH9SyDkPgc", "onclickToMyPosition", 1]]], [5, 12, 0.03, 0.05, false, false, -51]], [0, "55ATo4ToFOEYg93r0hQkWf", 1, 0], [5, 146, 108], [-279.5, -546.3, 0, 0, 0, 0, 1, 1, 1, 0]], [1, "change", 1, [[3, -52, [23], 24], [4, 3, -53, [[2, "4cb83Bxzk1BN7SH9SyDkPgc", "onClickChooseAreaBtn", 1]]], [6, 36, 0.03, 0.03, 0.15, false, false, false, -54]], [0, "5dUh9/Nz9JR4ME4Fj8VIHq", 1, 0], [5, 146, 108], [279.5, -412.9, 0, 0, 0, 0, 1, 1, 1, 0]], [1, "customer_service", 1, [[3, -55, [25], 26], [32, -56, [[2, "4cb83Bxzk1BN7SH9SyDkPgc", "notifyComingSoon", 1]]], [9, 9, -0.967236, 0.15, false, false, -57]], [0, "fb8/ncf4JH+Y4ABkYomO0s", 1, 0], [5, 88, 88], [-1056.4270000000001, 422.9, 0, 0, 0, 0, 1, 1, 1, 1]], [16, "bg", 512, 1, [[10, 0, -58, [0], 1], [7, 45, 100, 100, -59]], [0, "a42tBIu8ZDU5XRzOJjjGC0", 1, 0], [5, 750, 1334]], [1, "New Node", 2, [[23, 17, -60], [10, 0, -61, [2], 3]], [0, "7c0J5IQKROIpa+u0wg1TGc", 1, 0], [5, 750, 385], [0, -192.5, 0, 0, 0, 0, 1, 1, 1, 1]], [33, false, false, 4, 2], [19, "New Node", false, 1, [[17, "you", -62, [0, "9fwy1hwuNDF63Ir8FqVFmX", 1, 0], [0, 8, 0, 0, 0, 0, 1, 1, 1, 1]]], [0, "5cqaOEe8FGpYQbmJGDHqAf", 1, 0]]], 0, [0, 4, 1, 0, 0, 1, 0, 5, 17, 0, 6, 3, 0, 0, 1, 0, -1, 15, 0, -2, 4, 0, -3, 18, 0, -4, 6, 0, -5, 7, 0, -6, 8, 0, -7, 9, 0, -8, 10, 0, -9, 11, 0, -10, 12, 0, -11, 13, 0, -12, 14, 0, 0, 2, 0, 0, 2, 0, 0, 2, 0, -1, 16, 0, -2, 3, 0, 0, 3, 0, 0, 3, 0, 0, 3, 0, -1, 17, 0, 0, 4, 0, -1, 5, 0, 0, 5, 0, 0, 5, 0, 0, 6, 0, 0, 6, 0, 0, 6, 0, 0, 7, 0, 0, 7, 0, 0, 7, 0, 0, 8, 0, 0, 8, 0, 0, 8, 0, 0, 9, 0, 0, 9, 0, 0, 9, 0, 0, 10, 0, 0, 10, 0, 0, 10, 0, 0, 11, 0, 0, 11, 0, 0, 11, 0, 0, 12, 0, 0, 12, 0, 0, 12, 0, 0, 13, 0, 0, 13, 0, 0, 13, 0, 0, 14, 0, 0, 14, 0, 0, 14, 0, 0, 15, 0, 0, 15, 0, 0, 16, 0, 0, 16, 0, 3, 18, 0, 7, 1, 2, 3, 5, 62], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [-1, 1, -1, 1, -1, 1, -1, 1, -1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, 8], [0, 1, 0, 4, 0, 5, 0, 1, 0, 0, 6, 0, 7, 0, 8, 0, 9, 0, 10, 0, 11, 0, 12, 0, 13, 0, 14, 15]], [[{"name": "btn_share", "rect": [2, 4, 146, 108], "offset": [0, 0], "originalSize": [150, 116], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [16]], [[{"name": "btn_position", "rect": [2, 6, 146, 108], "offset": [0, -2], "originalSize": [150, 116], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [17]], [[{"name": "btn_setting_icon", "rect": [0, 0, 88, 88], "offset": [0, 0], "originalSize": [88, 88], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [18]], [[{"name": "btn_more", "rect": [2, 6, 146, 108], "offset": [0, -2], "originalSize": [150, 116], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [19]], [[{"name": "btn_customer", "rect": [0, 0, 88, 88], "offset": [0, 0], "originalSize": [88, 88], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [20]], [[{"name": "btn_change", "rect": [2, 6, 146, 108], "offset": [0, -2], "originalSize": [150, 116], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [21]], [[{"name": "btn_rank", "rect": [2, 6, 146, 108], "offset": [0, -2], "originalSize": [150, 116], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [22]], [[{"name": "bg", "rect": [0, 0, 750, 1672], "offset": [0, 0], "originalSize": [750, 1672], "capInsets": [0, 0, 0, 1251]}], [0], 0, [0], [2], [23]], [[{"name": "bg - 001", "rect": [0, 0, 750, 450], "offset": [0, 611], "originalSize": [750, 1672], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [24]], [[{"name": "bg2", "rect": [0, 0, 750, 1672], "offset": [0, 0], "originalSize": [750, 1672], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [25]]]]